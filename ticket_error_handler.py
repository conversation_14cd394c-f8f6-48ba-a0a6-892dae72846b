"""
Comprehensive error handling and validation system for the Discord ticket system.
This module provides robust error recovery, validation, and graceful degradation.
"""

import discord
import logging
import traceback
from enum import Enum
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timezone
import asyncio
import re

# Configure logging
logger = logging.getLogger('ticket_error_handler')

class ErrorSeverity(Enum):
    """Error severity levels for validation and error handling"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ValidationResult:
    """Utility class for managing validation results with errors, warnings, and data

    Note: The 'data' field can contain different types depending on the validator:
    - validate_text_content(): returns string
    - validate_color(): returns integer (color value)
    - validate_image_url(): returns string (URL) or None

    Always check the data type before calling methods like .get() on it.
    """

    def __init__(self, is_valid: bool = True, data: Any = None):
        self.is_valid = is_valid
        self.data = data
        self.errors = []
        self.warnings = []
        self.severity = ErrorSeverity.LOW
    
    def add_error(self, message: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM):
        """Add an error to the validation result"""
        self.errors.append({
            "message": message,
            "severity": severity.value,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
        self.is_valid = False
        if severity.value > self.severity.value:
            self.severity = severity
    
    def add_warning(self, message: str):
        """Add a warning to the validation result"""
        self.warnings.append(message)
    
    def merge(self, other: 'ValidationResult'):
        """Merge another validation result into this one"""
        if not other.is_valid:
            self.is_valid = False
        self.errors.extend(other.errors)
        self.warnings.extend(other.warnings)
        if other.severity.value > self.severity.value:
            self.severity = other.severity

class ContentValidator:
    """Validates content for Discord embeds and messages"""
    
    # Discord limits
    EMBED_TITLE_LIMIT = 256
    EMBED_DESCRIPTION_LIMIT = 4096
    EMBED_FIELD_NAME_LIMIT = 256
    EMBED_FIELD_VALUE_LIMIT = 1024
    EMBED_FOOTER_LIMIT = 2048
    
    def validate_text_content(self, content: str, field_name: str, max_length: int = None) -> ValidationResult:
        """Validate text content with length and character restrictions"""
        result = ValidationResult(data=content)
        
        if not content:
            result.data = ""
            return result
        
        # Determine max length based on field type
        if max_length is None:
            if field_name == "title":
                max_length = self.EMBED_TITLE_LIMIT
            elif field_name == "description":
                max_length = self.EMBED_DESCRIPTION_LIMIT
            elif field_name in ["field_name", "field_title"]:
                max_length = self.EMBED_FIELD_NAME_LIMIT
            elif field_name in ["field_value", "field_content"]:
                max_length = self.EMBED_FIELD_VALUE_LIMIT
            elif field_name in ["footer", "footer_text"]:
                max_length = self.EMBED_FOOTER_LIMIT
            else:
                max_length = 1000  # Default safe limit
        
        # Validate length
        if len(content) > max_length:
            truncated = content[:max_length-3] + "..."
            result.data = truncated
            result.add_warning(f"{field_name} was truncated from {len(content)} to {len(truncated)} characters")
        
        # Validate characters (remove problematic characters)
        cleaned_content = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', result.data)
        if cleaned_content != result.data:
            result.data = cleaned_content
            result.add_warning(f"{field_name} had invalid characters removed")
        
        return result
    
    def validate_color(self, color_input: Any) -> ValidationResult:
        """Validate Discord embed color"""
        result = ValidationResult()
        
        if color_input is None:
            result.data = 0x3498db  # Default blue
            return result
        
        try:
            if isinstance(color_input, str):
                # Handle hex colors
                if color_input.startswith('#'):
                    color_value = int(color_input[1:], 16)
                elif color_input.startswith('0x'):
                    color_value = int(color_input, 16)
                else:
                    # Try to parse as hex without prefix
                    color_value = int(color_input, 16)
            elif isinstance(color_input, int):
                color_value = color_input
            else:
                raise ValueError("Invalid color format")
            
            # Validate range
            if 0 <= color_value <= 0xFFFFFF:
                result.data = color_value
            else:
                result.data = 0x3498db  # Default blue
                result.add_warning("Color value out of range, using default blue")
                
        except (ValueError, TypeError):
            result.data = 0x3498db  # Default blue
            result.add_warning(f"Invalid color format '{color_input}', using default blue")
        
        return result
    
    async def validate_image_url(self, url: str) -> ValidationResult:
        """Validate image URL format and accessibility"""
        result = ValidationResult()
        
        if not url:
            result.data = None
            return result
        
        # Basic URL validation
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        if not url_pattern.match(url):
            result.add_error("Invalid URL format", ErrorSeverity.MEDIUM)
            result.data = None
            return result
        
        # Check for common image extensions
        image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.bmp']
        has_image_extension = any(url.lower().endswith(ext) for ext in image_extensions)
        
        if not has_image_extension:
            result.add_warning("URL may not be a direct image link")
        
        # Security check
        if url.startswith('http://'):
            result.add_warning("Consider using HTTPS for better security")
        
        result.data = url
        return result

class RecoverySystem:
    """Handles error recovery and fallback configurations"""
    
    def __init__(self):
        self.default_embed_config = {
            "title": "🎫 Support Tickets",
            "description": "Create a support ticket for assistance.",
            "color": 0x3498db,
            "footer_text": "Click a button below to create a ticket"
        }
        
        self.default_branding_config = {
            "server_name": "Support Server",
            "support_team": "Support Team",
            "contact_info": ""
        }
    
    async def recover_from_embed_error(self, config: Dict[str, Any], error: Exception) -> Dict[str, Any]:
        """Recover from embed configuration errors with fallback config"""
        logger.warning(f"Recovering from embed error: {error}")
        
        recovery_config = config.copy() if config else {}
        
        # Ensure embed section exists with defaults
        if "embed" not in recovery_config or not isinstance(recovery_config["embed"], dict):
            recovery_config["embed"] = self.default_embed_config.copy()
        else:
            # Fill in missing embed fields or fix None values
            for key, default_value in self.default_embed_config.items():
                if key not in recovery_config["embed"] or recovery_config["embed"][key] is None:
                    recovery_config["embed"][key] = default_value
        
        # Ensure branding section exists with defaults
        if "branding" not in recovery_config or not isinstance(recovery_config["branding"], dict):
            recovery_config["branding"] = self.default_branding_config.copy()
        else:
            # Fill in missing branding fields
            for key, default_value in self.default_branding_config.items():
                if key not in recovery_config["branding"]:
                    recovery_config["branding"][key] = default_value
        
        return recovery_config
    
    async def recover_from_permission_error(self, channel: discord.TextChannel, error: discord.Forbidden) -> Optional[str]:
        """Attempt to recover from permission errors"""
        logger.warning(f"Permission error in channel {channel.id}: {error}")
        
        # Check what permissions are available
        permissions = channel.permissions_for(channel.guild.me)
        
        if permissions.send_messages:
            return "Can send basic messages"
        elif permissions.view_channel:
            return "Can view channel but cannot send messages"
        else:
            return None
    
    def create_error_embed(self, message: str, severity: ErrorSeverity) -> discord.Embed:
        """Create a user-friendly error embed"""
        color_map = {
            ErrorSeverity.LOW: 0x95a5a6,      # Gray
            ErrorSeverity.MEDIUM: 0xf39c12,   # Orange
            ErrorSeverity.HIGH: 0xe74c3c,     # Red
            ErrorSeverity.CRITICAL: 0x8e44ad  # Purple
        }
        
        emoji_map = {
            ErrorSeverity.LOW: "ℹ️",
            ErrorSeverity.MEDIUM: "⚠️",
            ErrorSeverity.HIGH: "❌",
            ErrorSeverity.CRITICAL: "🚨"
        }
        
        embed = discord.Embed(
            title=f"{emoji_map[severity]} System Notice",
            description=message,
            color=color_map[severity],
            timestamp=datetime.now(timezone.utc)
        )
        
        embed.set_footer(text="If this issue persists, please contact an administrator")
        
        return embed

class TicketPanelErrorHandler:
    """Main error handler for ticket panel operations"""
    
    def __init__(self):
        self.content_validator = ContentValidator()
        self.recovery_system = RecoverySystem()
        self.error_stats = {}
    
    async def validate_customization_update(self, guild_id: int, config: Dict[str, Any]) -> ValidationResult:
        """Validate customization configuration updates"""
        result = ValidationResult()
        
        try:
            # Validate embed configuration
            if "embed" in config:
                embed_config = config["embed"]
                
                # Validate title
                if "title" in embed_config:
                    title_result = self.content_validator.validate_text_content(
                        embed_config["title"], "title"
                    )
                    result.merge(title_result)
                
                # Validate description
                if "description" in embed_config:
                    desc_result = self.content_validator.validate_text_content(
                        embed_config["description"], "description"
                    )
                    result.merge(desc_result)
                
                # Validate color
                if "color" in embed_config:
                    color_result = self.content_validator.validate_color(embed_config["color"])
                    result.merge(color_result)
                
                # Validate image URL
                if "image_url" in embed_config and embed_config["image_url"]:
                    image_result = await self.content_validator.validate_image_url(embed_config["image_url"])
                    result.merge(image_result)
            
            # Validate branding configuration
            if "branding" in config:
                branding_config = config["branding"]
                
                for field in ["server_name", "support_team", "contact_info"]:
                    if field in branding_config:
                        field_result = self.content_validator.validate_text_content(
                            branding_config[field], field, 100
                        )
                        result.merge(field_result)
            
        except Exception as e:
            result.add_error(f"Validation error: {str(e)}", ErrorSeverity.HIGH)
            logger.error(f"Error validating customization: {e}")
        
        return result
    
    async def validate_panel_creation(self, channel: discord.TextChannel, config: Dict[str, Any]) -> ValidationResult:
        """Validate panel creation prerequisites"""
        result = ValidationResult()
        
        try:
            # Check channel permissions
            permissions = channel.permissions_for(channel.guild.me)
            
            if not permissions.send_messages:
                result.add_error("Missing 'Send Messages' permission", ErrorSeverity.HIGH)
            
            if not permissions.embed_links:
                result.add_error("Missing 'Embed Links' permission", ErrorSeverity.MEDIUM)
            
            if not permissions.use_external_emojis:
                result.add_warning("Missing 'Use External Emojis' permission - some features may not work")
            
            # Validate configuration
            config_result = await self.validate_customization_update(channel.guild.id, config)
            result.merge(config_result)
            
        except Exception as e:
            result.add_error(f"Panel validation error: {str(e)}", ErrorSeverity.HIGH)
            logger.error(f"Error validating panel creation: {e}")
        
        return result
    
    async def handle_panel_creation_error(self, channel: discord.TextChannel, config: Dict[str, Any], error: Exception) -> Tuple[bool, Optional[discord.Embed]]:
        """Handle panel creation errors with recovery attempts"""
        logger.error(f"Panel creation error in channel {channel.id}: {error}")
        
        try:
            # Attempt to recover configuration
            recovery_config = await self.recovery_system.recover_from_embed_error(config, error)
            
            # Create error embed for user notification
            error_embed = self.recovery_system.create_error_embed(
                f"Panel creation encountered an issue but was recovered with default settings.\n\n"
                f"Error: {str(error)[:100]}{'...' if len(str(error)) > 100 else ''}",
                ErrorSeverity.MEDIUM
            )
            
            return True, error_embed
            
        except Exception as recovery_error:
            logger.error(f"Recovery failed: {recovery_error}")
            
            # Create critical error embed
            critical_embed = self.recovery_system.create_error_embed(
                f"Panel creation failed and recovery was unsuccessful.\n\n"
                f"Please contact an administrator for assistance.",
                ErrorSeverity.CRITICAL
            )
            
            return False, critical_embed

# Global instance
ticket_error_handler = TicketPanelErrorHandler()
