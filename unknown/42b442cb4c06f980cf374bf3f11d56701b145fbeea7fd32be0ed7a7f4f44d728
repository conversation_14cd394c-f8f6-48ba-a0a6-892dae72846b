import discord
from discord.ext import commands
import json
import os

# Load configuration
with open('Settings.json') as f:
	Settings = json.load(f)

# Get the token and prefix
token = Settings.get('Token')
prefix = Settings.get("Prefix")

# Set up intents
intents = discord.Intents.default()
intents.members = True
intents.presences = True
intents.message_content = True
intents.reactions = True

# Initialize the bot
bot = commands.Bot(command_prefix=prefix, intents=intents, help_command=None)