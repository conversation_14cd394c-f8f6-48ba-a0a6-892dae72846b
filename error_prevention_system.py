#!/usr/bin/env python3
"""
Error Prevention System for FiveM Bot
Provides runtime checks and validation to prevent recurring errors.
"""

import functools
import logging
import inspect
from typing import Any, Callable, Dict, List, Optional, Union

logger = logging.getLogger(__name__)

class ErrorPreventionSystem:
    """System to prevent common recurring errors in the FiveM bot"""
    
    @staticmethod
    def validate_method_signature(func: Callable, allowed_params: List[str]):
        """Decorator to validate method signatures and prevent invalid parameter usage"""
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Check for invalid parameters
            invalid_params = set(kwargs.keys()) - set(allowed_params)
            if invalid_params:
                method_name = func.__name__
                invalid_list = ', '.join(invalid_params)
                allowed_list = ', '.join(allowed_params)
                
                error_msg = (
                    f"{method_name}() got unexpected keyword argument(s): {invalid_list}. "
                    f"Allowed parameters: {allowed_list}"
                )
                logger.error(error_msg)
                raise TypeError(error_msg)
            
            return func(*args, **kwargs)
        
        return wrapper
    
    @staticmethod
    def safe_dict_access(obj: Any, key: str, default: Any = None, method_name: str = "unknown"):
        """Safely access dictionary-like objects, preventing .get() calls on strings"""
        
        if obj is None:
            logger.debug(f"{method_name}: Object is None, returning default")
            return default
        
        if isinstance(obj, str):
            error_msg = (
                f"{method_name}: Attempted to call .get() on string object. "
                f"String value: '{obj[:50]}...'. This would cause AttributeError."
            )
            logger.error(error_msg)
            raise AttributeError(f"'str' object has no attribute 'get'")
        
        if hasattr(obj, 'get') and callable(getattr(obj, 'get')):
            return obj.get(key, default)
        
        if hasattr(obj, key):
            return getattr(obj, key)
        
        logger.warning(f"{method_name}: Object {type(obj)} has no attribute '{key}', returning default")
        return default
    
    @staticmethod
    def validate_validation_result_usage(result: Any, expected_data_type: type = None):
        """Validate proper usage of ValidationResult objects"""
        
        if not hasattr(result, 'data'):
            raise ValueError("Object is not a ValidationResult - missing 'data' attribute")
        
        if not hasattr(result, 'is_valid'):
            raise ValueError("Object is not a ValidationResult - missing 'is_valid' attribute")
        
        if expected_data_type and result.data is not None:
            if not isinstance(result.data, expected_data_type):
                logger.warning(
                    f"ValidationResult.data has unexpected type: {type(result.data)}, "
                    f"expected: {expected_data_type}"
                )
        
        return True
    
    @staticmethod
    def safe_image_url_processing(image_result: Any) -> Optional[str]:
        """Safely process image validation results to prevent AttributeError"""
        
        try:
            # Validate it's a proper ValidationResult
            ErrorPreventionSystem.validate_validation_result_usage(image_result, str)
            
            if not image_result.is_valid or not image_result.data:
                return None
            
            # Ensure data is a string (URL)
            if isinstance(image_result.data, str):
                return image_result.data
            else:
                logger.error(f"Image validation result data is not a string: {type(image_result.data)}")
                return None
                
        except Exception as e:
            logger.error(f"Error in safe_image_url_processing: {e}")
            return None

# Decorator for validate_text_content method
def validate_text_content_signature(func):
    """Specific decorator for validate_text_content to prevent 'required' parameter"""
    allowed_params = ['content', 'field_name', 'max_length']
    return ErrorPreventionSystem.validate_method_signature(func, allowed_params)

# Runtime validation functions
def check_for_common_errors():
    """Runtime check for common error patterns"""
    
    checks_passed = []
    
    # Check 1: Verify ContentValidator method signature
    try:
        from ticket_error_handler import ContentValidator
        validator = ContentValidator()
        
        # Test correct usage
        result = validator.validate_text_content("test", "field", 100)
        checks_passed.append("ContentValidator.validate_text_content works correctly")
        
        # Test that invalid usage fails
        try:
            validator.validate_text_content("test", "field", 100, required=True)
            checks_passed.append("ERROR: validate_text_content accepts invalid 'required' parameter")
        except TypeError:
            checks_passed.append("ContentValidator correctly rejects invalid parameters")
            
    except Exception as e:
        checks_passed.append(f"ERROR: ContentValidator check failed: {e}")
    
    # Check 2: Verify ValidationResult data types
    try:
        from ticket_error_handler import ValidationResult
        
        # Test string data
        result = ValidationResult(data="test_string")
        if isinstance(result.data, str):
            checks_passed.append("ValidationResult handles string data correctly")
        
        # Test that .get() on string data fails appropriately
        try:
            url = result.data.get("url", "fallback")
            checks_passed.append("ERROR: .get() call on string data succeeded")
        except AttributeError:
            checks_passed.append("ValidationResult.data correctly prevents .get() on strings")
            
    except Exception as e:
        checks_passed.append(f"ERROR: ValidationResult check failed: {e}")
    
    return checks_passed

def log_prevention_status():
    """Log the current status of error prevention measures"""
    
    logger.info("=== Error Prevention System Status ===")
    
    checks = check_for_common_errors()
    for check in checks:
        if check.startswith("ERROR:"):
            logger.error(check)
        else:
            logger.info(f"✅ {check}")
    
    logger.info("=== End Error Prevention Status ===")

# Example usage functions
def safe_embed_image_processing(image_result):
    """Example of safe image processing to prevent AttributeError"""
    
    url = ErrorPreventionSystem.safe_image_url_processing(image_result)
    if url:
        return url
    else:
        logger.warning("Image processing failed, using fallback")
        return None

def safe_config_access(config, key, default=None):
    """Example of safe configuration access"""
    
    return ErrorPreventionSystem.safe_dict_access(
        config, key, default, method_name="safe_config_access"
    )

if __name__ == "__main__":
    # Run checks when script is executed directly
    print("Running Error Prevention System checks...")
    log_prevention_status()
