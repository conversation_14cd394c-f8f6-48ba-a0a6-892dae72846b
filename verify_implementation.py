#!/usr/bin/env python3
"""
Implementation verification script for ticket panel auto-recreation system.
This script verifies that all components are properly implemented and working.
"""

import os
import sys
import re
from datetime import datetime

def check_file_exists(filepath):
    """Check if a file exists and return its status"""
    if os.path.exists(filepath):
        return True, f"✅ {filepath} exists"
    else:
        return False, f"❌ {filepath} missing"

def check_function_in_file(filepath, function_name):
    """Check if a function exists in a file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            if f"def {function_name}" in content or f"async def {function_name}" in content:
                return True, f"✅ {function_name} found in {filepath}"
            else:
                return False, f"❌ {function_name} not found in {filepath}"
    except Exception as e:
        return False, f"❌ Error reading {filepath}: {e}"

def check_import_in_file(filepath, import_statement):
    """Check if an import statement exists in a file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            if import_statement in content:
                return True, f"✅ Import '{import_statement}' found in {filepath}"
            else:
                return False, f"❌ Import '{import_statement}' not found in {filepath}"
    except Exception as e:
        return False, f"❌ Error reading {filepath}: {e}"

def check_code_pattern(filepath, pattern, description):
    """Check if a code pattern exists in a file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            if re.search(pattern, content, re.MULTILINE | re.DOTALL):
                return True, f"✅ {description} found in {filepath}"
            else:
                return False, f"❌ {description} not found in {filepath}"
    except Exception as e:
        return False, f"❌ Error reading {filepath}: {e}"

def verify_implementation():
    """Verify the complete ticket panel auto-recreation implementation"""
    print("🔍 TICKET PANEL AUTO-RECREATION IMPLEMENTATION VERIFICATION")
    print("=" * 70)
    print(f"Verification started at: {datetime.now()}")
    
    all_checks_passed = True
    
    # Check 1: Required files exist
    print("\n📁 Checking required files...")
    files_to_check = [
        "bot.py",
        "tickets.py",
        "startup_test.py",
        "test_ticket_panel_recreation.py",
        "TICKET_PANEL_AUTO_RECREATION_DOCUMENTATION.md"
    ]
    
    for filepath in files_to_check:
        passed, message = check_file_exists(filepath)
        print(f"  {message}")
        if not passed:
            all_checks_passed = False
    
    # Check 2: Bot.py modifications
    print("\n🤖 Checking bot.py modifications...")
    bot_checks = [
        ("auto_recreate_ticket_panels", "auto_recreate_ticket_panels function import"),
        ("from tickets import auto_recreate_ticket_panels", "auto_recreate_ticket_panels import statement"),
        ("await auto_recreate_ticket_panels()", "auto_recreate_ticket_panels function call")
    ]
    
    for check, description in bot_checks:
        passed, message = check_import_in_file("bot.py", check)
        print(f"  {message}")
        if not passed:
            all_checks_passed = False
    
    # Check 3: Tickets.py functions
    print("\n🎫 Checking tickets.py functions...")
    ticket_functions = [
        "auto_recreate_ticket_panels",
        "create_simple_ticket_panel",
        "load_ticket_data",
        "recreate_ticket_panels_command"
    ]
    
    for func in ticket_functions:
        passed, message = check_function_in_file("tickets.py", func)
        print(f"  {message}")
        if not passed:
            all_checks_passed = False
    
    # Check 4: Tickets.py classes
    print("\n🏗️ Checking tickets.py classes...")
    ticket_classes = [
        "SimpleTicketPanelView",
        "SimpleTicketCategoryButton"
    ]
    
    for cls in ticket_classes:
        passed, message = check_code_pattern("tickets.py", f"class {cls}", f"Class {cls}")
        print(f"  {message}")
        if not passed:
            all_checks_passed = False
    
    # Check 5: Enhanced auto_recreate_ticket_panels function
    print("\n🔧 Checking enhanced auto_recreate_ticket_panels function...")
    enhanced_features = [
        (r"panels_created.*panels_failed", "Success/failure tracking"),
        (r"logger\.info.*Starting automatic ticket panel recreation", "Enhanced logging"),
        (r"clear_old_ticket_views", "Old view cleanup"),
        (r"create_modern_ticket_panel.*create_simple_ticket_panel", "Fallback mechanism"),
        (r"guild_ticket_config.*global.*fallback", "Global config fallback")
    ]
    
    for pattern, description in enhanced_features:
        passed, message = check_code_pattern("tickets.py", pattern, description)
        print(f"  {message}")
        if not passed:
            all_checks_passed = False
    
    # Check 6: Bot.py startup integration
    print("\n🚀 Checking bot.py startup integration...")
    startup_patterns = [
        (r"Loading ticket configuration.*auto_recreate_ticket_panels", "Auto-recreation in startup sequence"),
        (r"Successfully imported auto_recreate_ticket_panels", "Import success logging"),
        (r"Ticket panels auto-recreation completed", "Completion logging")
    ]
    
    for pattern, description in startup_patterns:
        passed, message = check_code_pattern("bot.py", pattern, description)
        print(f"  {message}")
        if not passed:
            all_checks_passed = False
    
    # Check 7: Test files
    print("\n🧪 Checking test files...")
    test_checks = [
        ("startup_test.py", "test_startup_sequence", "Startup test function"),
        ("test_ticket_panel_recreation.py", "test_ticket_panel_recreation", "Recreation test function")
    ]
    
    for filepath, func, description in test_checks:
        passed, message = check_function_in_file(filepath, func)
        print(f"  {message}")
        if not passed:
            all_checks_passed = False
    
    # Final result
    print("\n" + "=" * 70)
    if all_checks_passed:
        print("🎉 ALL CHECKS PASSED! Implementation is complete and ready.")
        print("\n✅ Next steps:")
        print("  1. Start your bot")
        print("  2. Check console logs for auto-recreation messages")
        print("  3. Use /recreate_ticket_panels command for manual testing")
        print("  4. Restart bot to verify automatic recreation")
        print("  5. Run startup_test.py for comprehensive testing")
    else:
        print("❌ SOME CHECKS FAILED! Please review the issues above.")
        print("\n🔧 Troubleshooting:")
        print("  1. Check that all files are in the correct location")
        print("  2. Verify that modifications were saved properly")
        print("  3. Review the implementation documentation")
        print("  4. Check for syntax errors in the code")
    
    print(f"\nVerification completed at: {datetime.now()}")
    return all_checks_passed

if __name__ == "__main__":
    success = verify_implementation()
    sys.exit(0 if success else 1)
