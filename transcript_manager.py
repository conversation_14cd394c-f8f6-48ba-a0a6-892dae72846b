import discord
import asyncio
import pymongo
import motor.motor_asyncio
import logging
import time
import random
import io
import os
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union
from bson import ObjectId
import traceback
from collections import defaultdict, deque
import aiohttp
import hashlib
import re
from pymongo import IndexModel, ASCENDING, DESCENDING, TEXT

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('transcript_manager')

class TranscriptManager:
    """Advanced transcript management system for high-scale ticket operations"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(TranscriptManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        # MongoDB connection settings with enhanced pooling for high scale
        self.mongo_uri = "mongodb://localhost:27017/"
        self.db_name = "missminutesbot"
        self.client = None
        self.db = None
        self.transcript_collection = None
        self.transcript_stats_collection = None
        self.transcript_search_collection = None
        self.transcript_archive_collection = None

        # Enhanced cache settings for high performance
        self.cache = {}
        self.cache_ttl = {}
        self.default_ttl = 600  # 10 minutes default TTL for better performance
        self.max_cache_size = 50000  # Increased cache size for high load
        self.cache_hits = 0
        self.cache_misses = 0
        self.cache_evictions = 0
        self.cache_lock = asyncio.Lock()  # Lock for thread-safe cache operations

        # Secondary cache for frequently accessed items
        self.hot_cache = {}  # Items accessed more than 5 times
        self.access_counts = {}
        self.hot_cache_threshold = 5
        self.hot_cache_ttl = 3600  # 1 hour TTL for hot items

        # Enhanced rate limiting for high concurrency
        self.rate_limits = {}
        self.queues = {}
        self.processing = {}
        self.max_retries = 20  # Increased for better resilience
        self.base_delay = 0.5  # Start with lower delay
        self.max_delay = 600.0
        self.priority_queues = {}  # For critical operations

        # Circuit breaker for preventing cascading failures
        self.circuit_breakers = {}
        self.failure_thresholds = defaultdict(lambda: 10)
        self.reset_timeout = 60  # seconds

        # Enhanced bulk operations
        self.bulk_queue = asyncio.Queue()
        self.bulk_processing = False
        self.batch_sizes = defaultdict(lambda: 10)
        self.success_threshold = 5
        self.success_counts = defaultdict(int)
        self.max_batch_size = 100  # Maximum batch size
        self.min_batch_size = 1    # Minimum batch size

        # Connection state with health monitoring
        self.is_connected = False
        self.last_connection_attempt = 0
        self.connection_retry_delay = 5  # seconds
        self.connection_lock = asyncio.Lock()
        self.connection_health = 1.0  # 0.0 to 1.0 health score
        self.health_check_interval = 30  # seconds
        self.last_health_check = 0

        # Enhanced performance metrics
        self.operation_times = defaultdict(list)
        self.operation_counts = defaultdict(int)
        self.error_counts = defaultdict(int)
        self.latency_history = deque(maxlen=100)  # Track recent latencies
        self.throughput_history = deque(maxlen=100)  # Track recent throughput

        # Sharding support
        self.shard_key = "guild_id"  # Default shard key
        self.shard_enabled = False
        self.shard_count = 1

        # Compression settings
        self.compression_enabled = True
        self.compression_level = 6  # 0-9, higher is more compression but slower
        self.compression_threshold = 1024  # Only compress data larger than this

        self._initialized = True

    async def connect(self, force=False):
        """Connect to MongoDB with enhanced connection pooling and sharding awareness for high scale"""
        # Use a lock to prevent multiple connection attempts
        async with self.connection_lock:
            # Check if already connected and connection is healthy
            if self.is_connected and not force:
                # Perform periodic health check
                current_time = time.time()
                if current_time - self.last_health_check > self.health_check_interval:
                    try:
                        # Non-blocking health check
                        health_task = asyncio.create_task(self._check_connection_health())
                        await asyncio.wait_for(health_task, timeout=2.0)
                    except asyncio.TimeoutError:
                        logger.warning("Health check timed out, connection may be degraded")
                        self.connection_health = max(0.0, self.connection_health - 0.2)
                    except Exception as e:
                        logger.warning(f"Health check failed: {e}")
                        self.connection_health = max(0.0, self.connection_health - 0.3)

                # If connection health is good, return
                if self.connection_health > 0.5:
                    return True
                # Otherwise, force reconnection
                logger.warning(f"Connection health degraded ({self.connection_health:.2f}), forcing reconnection")
                force = True

            # Implement connection throttling with exponential backoff
            current_time = time.time()
            if not force and current_time - self.last_connection_attempt < self.connection_retry_delay:
                logger.warning(f"Connection attempt throttled. Waiting {self.connection_retry_delay}s before retry.")
                return False

            self.last_connection_attempt = current_time
            # Increase retry delay for next attempt (up to 60 seconds)
            self.connection_retry_delay = min(60, self.connection_retry_delay * 1.5)

            try:
                # Close existing connection if any
                if self.client:
                    try:
                        self.client.close()
                    except:
                        pass

                # Configure connection with enhanced pooling for extreme high scale
                self.client = motor.motor_asyncio.AsyncIOMotorClient(
                    self.mongo_uri,
                    maxPoolSize=200,  # Increased for extreme concurrency (100k users)
                    minPoolSize=20,   # Maintain more minimum connections
                    maxIdleTimeMS=60000,  # Keep idle connections longer (1 minute)
                    connectTimeoutMS=10000,  # 10 second connection timeout for reliability
                    serverSelectionTimeoutMS=10000,  # 10 second server selection timeout
                    socketTimeoutMS=30000,  # Socket timeout for long operations
                    retryWrites=True,  # Enable retryable writes
                    retryReads=True,   # Enable retryable reads
                    w="majority",  # Write concern for data durability
                    readPreference="secondaryPreferred",  # Read from secondaries when possible
                    appName="MissMinutesBot-TicketSystem",  # Application name for monitoring
                    waitQueueTimeoutMS=10000,  # Wait queue timeout
                    zlibCompressionLevel=6  # Enable network compression
                )

                # Test connection with timeout
                await asyncio.wait_for(self.client.admin.command('ping'), timeout=5.0)

                self.db = self.client[self.db_name]
                self.is_connected = True
                self.connection_health = 1.0  # Reset health score
                self.connection_retry_delay = 5  # Reset retry delay
                self.last_health_check = current_time

                # Initialize collections with namespace validation
                self.transcript_collection = self.db["transcripts"]
                self.transcript_stats_collection = self.db["transcript_stats"]
                self.transcript_search_collection = self.db["transcript_search"]
                self.transcript_archive_collection = self.db["transcript_archive"]

                # Create indexes for high performance
                await asyncio.wait_for(self._create_indexes(), timeout=30.0)

                # Check for sharding configuration
                try:
                    is_sharded = await self._check_sharding_status()
                    if is_sharded:
                        logger.info("MongoDB is running in sharded mode")
                        self.shard_enabled = True
                    else:
                        logger.info("MongoDB is running in non-sharded mode")
                except Exception as e:
                    logger.warning(f"Could not determine sharding status: {e}")

                # Log successful connection with detailed info
                logger.info(f"Successfully connected to MongoDB with optimized connection pooling for high scale")
                logger.info(f"Connection settings: maxPoolSize={200}, minPoolSize={20}")
                return True

            except asyncio.TimeoutError:
                self.is_connected = False
                logger.error("Connection to MongoDB timed out")
                return False
            except Exception as e:
                self.is_connected = False
                logger.error(f"Error connecting to MongoDB: {e}")
                traceback.print_exc()
                return False

    async def _check_connection_health(self):
        """Check the health of the MongoDB connection"""
        try:
            # Measure response time
            start_time = time.time()
            await self.client.admin.command('ping')
            response_time = time.time() - start_time

            # Update health metrics
            self.latency_history.append(response_time)
            avg_latency = sum(self.latency_history) / len(self.latency_history)

            # Calculate health score (0.0 to 1.0) based on response time
            # Lower is better, anything over 500ms is concerning
            latency_score = max(0.0, min(1.0, 1.0 - (avg_latency / 0.5)))

            # Update overall health score (weighted average)
            self.connection_health = (self.connection_health * 0.7) + (latency_score * 0.3)
            self.last_health_check = time.time()

            logger.debug(f"Connection health: {self.connection_health:.2f}, Latency: {response_time*1000:.2f}ms")
            return True
        except Exception as e:
            logger.warning(f"Connection health check failed: {e}")
            self.connection_health = max(0.0, self.connection_health - 0.3)
            return False

    async def _check_sharding_status(self):
        """Check if MongoDB is running in sharded mode"""
        try:
            # Try to access the config database which only exists in sharded clusters
            config_db = self.client.config
            collections = await config_db.list_collection_names()
            return 'shards' in collections
        except Exception:
            return False

    async def _create_indexes(self):
        """Create optimized indexes for transcript collections"""
        try:
            # Transcript collection indexes
            transcript_indexes = [
                IndexModel([("ticket_id", ASCENDING)], unique=True, background=True),
                IndexModel([("guild_id", ASCENDING), ("closed_at", DESCENDING)], background=True),
                IndexModel([("closed_by", ASCENDING)], background=True),
                IndexModel([("created_at", DESCENDING)], background=True),
                IndexModel([("category", ASCENDING), ("closed_at", DESCENDING)], background=True),
                # TTL index for automatic archiving after 90 days (can be adjusted)
                IndexModel([("closed_at", ASCENDING)], expireAfterSeconds=7776000, background=True)
            ]

            # Search collection indexes
            search_indexes = [
                IndexModel([("ticket_id", ASCENDING)], background=True),
                IndexModel([("content", TEXT)], background=True, default_language="english"),
                IndexModel([("author_id", ASCENDING)], background=True)
            ]

            # Stats collection indexes
            stats_indexes = [
                IndexModel([("guild_id", ASCENDING)], background=True),
                IndexModel([("date", DESCENDING)], background=True),
                IndexModel([("category", ASCENDING)], background=True)
            ]

            # Create all indexes
            await asyncio.gather(
                self.transcript_collection.create_indexes(transcript_indexes),
                self.transcript_search_collection.create_indexes(search_indexes),
                self.transcript_stats_collection.create_indexes(stats_indexes)
            )

            logger.info("Created optimized indexes for transcript collections")
            return True
        except Exception as e:
            logger.error(f"Error creating indexes: {e}")
            traceback.print_exc()
            return False

    async def store_transcript(self, ticket_data: Dict[str, Any], messages: List[Dict[str, Any]]) -> str:
        """Store a transcript with advanced metadata and search capabilities"""
        if not await self.connect():
            logger.error("Failed to connect to MongoDB")
            return None

        try:
            start_time = time.time()

            # Generate a unique ID for the transcript
            transcript_id = str(ObjectId())

            # Process and enhance ticket data
            enhanced_ticket_data = {
                "_id": transcript_id,
                "ticket_id": ticket_data.get("ticket_id"),
                "guild_id": ticket_data.get("guild_id"),
                "channel_id": ticket_data.get("channel_id"),
                "category": ticket_data.get("category", "general"),
                "created_by": ticket_data.get("created_by"),
                "created_at": ticket_data.get("created_at", datetime.now().isoformat()),
                "closed_by": ticket_data.get("closed_by"),
                "close_reason": ticket_data.get("close_reason"),
                "closed_at": ticket_data.get("closed_at", datetime.now().isoformat()),
                "resolution_time": self._calculate_resolution_time(
                    ticket_data.get("created_at"),
                    ticket_data.get("closed_at")
                ),
                "message_count": len(messages),
                "participants": self._extract_participants(messages),
                "staff_participants": self._extract_staff_participants(
                    messages,
                    ticket_data.get("staff_roles", [])
                ),
                "first_response_time": self._calculate_first_response_time(
                    messages,
                    ticket_data.get("created_by"),
                    ticket_data.get("staff_roles", [])
                ),
                "tags": ticket_data.get("tags", []),
                "metadata": ticket_data.get("metadata", {}),
                "messages": messages
            }

            # Store the transcript
            await self.transcript_collection.insert_one(enhanced_ticket_data)

            # Create searchable content for each message
            search_documents = []
            for i, msg in enumerate(messages):
                search_documents.append({
                    "transcript_id": transcript_id,
                    "ticket_id": ticket_data.get("ticket_id"),
                    "message_index": i,
                    "author_id": msg.get("author_id"),
                    "author_name": msg.get("author_name"),
                    "content": msg.get("content", ""),
                    "timestamp": msg.get("timestamp")
                })

            # Bulk insert search documents if there are any
            if search_documents:
                await self.transcript_search_collection.insert_many(search_documents)

            # Update statistics
            await self._update_statistics(enhanced_ticket_data)

            # Log performance
            elapsed = time.time() - start_time
            self.operation_times["store_transcript"].append(elapsed)
            self.operation_counts["store_transcript"] += 1
            logger.info(f"Stored transcript {transcript_id} in {elapsed:.2f}s")

            return transcript_id

        except Exception as e:
            self.error_counts["store_transcript"] += 1
            logger.error(f"Error storing transcript: {e}")
            traceback.print_exc()
            return None

    async def get_transcript(self, transcript_id: str = None, ticket_id: str = None) -> Dict[str, Any]:
        """Retrieve a transcript by ID with enhanced multi-level caching for high performance"""
        if not await self.connect():
            return None

        try:
            # Determine which ID to use
            query = {}
            cache_key = None

            if transcript_id:
                query = {"_id": transcript_id}
                cache_key = f"transcript:{transcript_id}"
            elif ticket_id:
                query = {"ticket_id": ticket_id}
                cache_key = f"transcript:ticket:{ticket_id}"
            else:
                logger.error("No ID provided to get_transcript")
                return None

            # Thread-safe cache access
            async with self.cache_lock:
                # Check hot cache first (most frequently accessed items)
                if cache_key in self.hot_cache:
                    if time.time() < self.cache_ttl.get(f"hot:{cache_key}", 0):
                        self.cache_hits += 1
                        # Update access metrics
                        self.access_counts[cache_key] = self.access_counts.get(cache_key, 0) + 1
                        return self.hot_cache[cache_key]

                # Check regular cache next
                if cache_key in self.cache:
                    if time.time() < self.cache_ttl.get(cache_key, 0):
                        self.cache_hits += 1

                        # Update access count for this item
                        self.access_counts[cache_key] = self.access_counts.get(cache_key, 0) + 1

                        # If this item is frequently accessed, promote to hot cache
                        if self.access_counts[cache_key] >= self.hot_cache_threshold:
                            self.hot_cache[cache_key] = self.cache[cache_key]
                            self.cache_ttl[f"hot:{cache_key}"] = time.time() + self.hot_cache_ttl
                            logger.debug(f"Promoted {cache_key} to hot cache (access count: {self.access_counts[cache_key]})")

                        return self.cache[cache_key]
                    else:
                        # Expired cache - remove it
                        del self.cache[cache_key]
                        if cache_key in self.cache_ttl:
                            del self.cache_ttl[cache_key]
                        self.cache_evictions += 1

                self.cache_misses += 1

            # Fetch from database with timeout protection
            start_time = time.time()
            try:
                # Use asyncio.wait_for to prevent hanging on slow DB operations
                transcript = await asyncio.wait_for(self.transcript_collection.find_one(query), timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning(f"Database query timeout for {cache_key}")
                return None

            # Only cache if we got a result
            if transcript:
                async with self.cache_lock:
                    # Apply compression for large transcripts if enabled
                    if self.compression_enabled and len(str(transcript)) > self.compression_threshold:
                        # In a real implementation, you would compress the data here
                        # For now, we'll just store it as is
                        pass

                    # Store in regular cache
                    self.cache[cache_key] = transcript
                    self.cache_ttl[cache_key] = time.time() + self.default_ttl

                    # Initialize access count
                    self.access_counts[cache_key] = 1

                    # Manage cache size - more efficient eviction policy
                    if len(self.cache) > self.max_cache_size:
                        # Evict based on a combination of age and access frequency
                        # This is a simple LRU + frequency hybrid approach
                        eviction_candidates = []
                        current_time = time.time()

                        for k, expire_time in self.cache_ttl.items():
                            if k.startswith("hot:"):
                                continue  # Skip hot cache keys

                            # Calculate a score based on time left and access count
                            # Lower score = better eviction candidate
                            time_left = max(0, expire_time - current_time)
                            access_count = self.access_counts.get(k, 0)

                            # Score formula: time left (normalized) + access count weight
                            # Items accessed more frequently and with more time left score higher
                            score = (time_left / self.default_ttl) + (min(access_count, 10) / 10)

                            eviction_candidates.append((k, score))

                        # Sort by score (ascending) and take the lowest 10% for eviction
                        eviction_candidates.sort(key=lambda x: x[1])
                        to_evict = eviction_candidates[:max(100, len(eviction_candidates) // 10)]

                        # Perform eviction
                        for k, _ in to_evict:
                            if k in self.cache:
                                del self.cache[k]
                            if k in self.cache_ttl:
                                del self.cache_ttl[k]
                            if k in self.access_counts:
                                del self.access_counts[k]
                            self.cache_evictions += 1

                        logger.debug(f"Cache eviction performed: {len(to_evict)} items removed")

            # Log performance metrics
            elapsed = time.time() - start_time
            self.operation_times["get_transcript"].append(elapsed)
            self.operation_counts["get_transcript"] += 1
            self.latency_history.append(elapsed)

            # Log detailed metrics periodically
            if self.operation_counts["get_transcript"] % 100 == 0:
                hit_ratio = self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0
                avg_latency = sum(self.latency_history) / len(self.latency_history) if self.latency_history else 0
                logger.info(f"Transcript cache stats: hit ratio={hit_ratio:.2f}, evictions={self.cache_evictions}, " +
                           f"avg latency={avg_latency*1000:.2f}ms, cache size={len(self.cache)}, hot cache size={len(self.hot_cache)}")

            return transcript

        except Exception as e:
            self.error_counts["get_transcript"] += 1
            logger.error(f"Error retrieving transcript: {e}")
            traceback.print_exc()
            return None

    async def search_transcripts(self,
                               guild_id: int = None,
                               query: str = None,
                               category: str = None,
                               user_id: int = None,
                               staff_id: int = None,
                               date_from: str = None,
                               date_to: str = None,
                               tags: List[str] = None,
                               limit: int = 20,
                               skip: int = 0) -> List[Dict[str, Any]]:
        """Advanced search for transcripts with multiple filters"""
        if not await self.connect():
            return []

        try:
            start_time = time.time()

            # Build the search query
            search_query = {}

            if guild_id:
                search_query["guild_id"] = guild_id

            if category:
                search_query["category"] = category

            if user_id:
                search_query["participants"] = user_id

            if staff_id:
                search_query["staff_participants"] = staff_id

            # Date range filtering
            if date_from or date_to:
                date_query = {}
                if date_from:
                    date_query["$gte"] = date_from
                if date_to:
                    date_query["$lte"] = date_to
                if date_query:
                    search_query["closed_at"] = date_query

            # Tag filtering
            if tags and len(tags) > 0:
                search_query["tags"] = {"$all": tags}

            # Text search handling
            if query:
                # First find matching message IDs from the search collection
                text_search = await self.transcript_search_collection.find(
                    {"$text": {"$search": query}}
                ).distinct("transcript_id")

                if text_search:
                    search_query["_id"] = {"$in": text_search}
                else:
                    # No matches found
                    return []

            # Execute the search
            cursor = self.transcript_collection.find(
                search_query,
                # Exclude the full messages array for performance
                {"messages": 0}
            ).sort("closed_at", -1).skip(skip).limit(limit)

            results = await cursor.to_list(length=limit)

            # Log performance
            elapsed = time.time() - start_time
            self.operation_times["search_transcripts"].append(elapsed)
            self.operation_counts["search_transcripts"] += 1

            return results

        except Exception as e:
            self.error_counts["search_transcripts"] += 1
            logger.error(f"Error searching transcripts: {e}")
            traceback.print_exc()
            return []

    async def get_transcript_statistics(self,
                                      guild_id: int,
                                      period: str = "week",
                                      category: str = None) -> Dict[str, Any]:
        """Get statistics about tickets and transcripts"""
        if not await self.connect():
            return {}

        try:
            start_time = time.time()

            # Determine date range based on period
            now = datetime.now()
            date_from = None

            if period == "day":
                date_from = (now - timedelta(days=1)).isoformat()
            elif period == "week":
                date_from = (now - timedelta(days=7)).isoformat()
            elif period == "month":
                date_from = (now - timedelta(days=30)).isoformat()
            elif period == "year":
                date_from = (now - timedelta(days=365)).isoformat()

            # Build the query
            query = {"guild_id": guild_id}

            if date_from:
                query["closed_at"] = {"$gte": date_from}

            if category:
                query["category"] = category

            # Get total count
            total_count = await self.transcript_collection.count_documents(query)

            # Get average resolution time
            pipeline = [
                {"$match": query},
                {"$group": {
                    "_id": None,
                    "avg_resolution_time": {"$avg": "$resolution_time"},
                    "avg_first_response_time": {"$avg": "$first_response_time"},
                    "avg_messages": {"$avg": "$message_count"}
                }}
            ]

            stats_result = await self.transcript_collection.aggregate(pipeline).to_list(length=1)

            # Get category distribution
            category_pipeline = [
                {"$match": {"guild_id": guild_id}},
                {"$group": {
                    "_id": "$category",
                    "count": {"$sum": 1}
                }},
                {"$sort": {"count": -1}}
            ]

            categories = await self.transcript_collection.aggregate(category_pipeline).to_list(length=10)

            # Get staff performance
            staff_pipeline = [
                {"$match": query},
                {"$unwind": "$staff_participants"},
                {"$group": {
                    "_id": "$staff_participants",
                    "tickets_handled": {"$sum": 1},
                    "avg_resolution_time": {"$avg": "$resolution_time"}
                }},
                {"$sort": {"tickets_handled": -1}}
            ]

            staff_stats = await self.transcript_collection.aggregate(staff_pipeline).to_list(length=10)

            # Compile results
            stats = {
                "total_tickets": total_count,
                "period": period,
                "category": category,
                "categories": {item["_id"]: item["count"] for item in categories},
                "staff_performance": staff_stats
            }

            # Add averages if available
            if stats_result and len(stats_result) > 0:
                stats["avg_resolution_time"] = stats_result[0].get("avg_resolution_time", 0)
                stats["avg_first_response_time"] = stats_result[0].get("avg_first_response_time", 0)
                stats["avg_messages"] = stats_result[0].get("avg_messages", 0)

            # Log performance
            elapsed = time.time() - start_time
            self.operation_times["get_transcript_statistics"].append(elapsed)
            self.operation_counts["get_transcript_statistics"] += 1

            return stats

        except Exception as e:
            self.error_counts["get_transcript_statistics"] += 1
            logger.error(f"Error getting transcript statistics: {e}")
            traceback.print_exc()
            return {}

    async def export_transcript(self,
                              transcript_id: str = None,
                              ticket_id: str = None,
                              format: str = "txt") -> Optional[Union[str, bytes]]:
        """Export a transcript in various formats (txt, html, json, pdf)"""
        transcript = await self.get_transcript(transcript_id, ticket_id)
        if not transcript:
            return None

        try:
            if format.lower() == "txt":
                return self._format_transcript_txt(transcript)
            elif format.lower() == "html":
                return self._format_transcript_html(transcript)
            elif format.lower() == "json":
                return json.dumps(transcript, default=str)
            elif format.lower() == "pdf":
                return await self._format_transcript_pdf(transcript)
            else:
                logger.error(f"Unsupported export format: {format}")
                return None
        except Exception as e:
            logger.error(f"Error exporting transcript: {e}")
            traceback.print_exc()
            return None

    def _format_transcript_txt(self, transcript: Dict[str, Any]) -> str:
        """Format transcript as plain text"""
        lines = []

        # Add header
        lines.append(f"Ticket Transcript: {transcript.get('ticket_id', 'Unknown')}")
        lines.append(f"Created: {transcript.get('created_at', 'Unknown')}")
        lines.append(f"Closed: {transcript.get('closed_at', 'Unknown')}")
        lines.append(f"Reason: {transcript.get('close_reason', 'No reason provided')}")
        lines.append("-" * 50)

        # Add messages
        for msg in transcript.get("messages", []):
            timestamp = msg.get("timestamp", "")
            author = msg.get("author_name", "Unknown")
            content = msg.get("content", "")

            lines.append(f"[{timestamp}] {author}: {content}")

            # Add attachments if any
            for attachment in msg.get("attachments", []):
                lines.append(f"[Attachment: {attachment}]")

            lines.append("")

        return "\n".join(lines)

    def _format_transcript_html(self, transcript: Dict[str, Any]) -> str:
        """Format transcript as HTML with professional styling"""
        try:
            # Get current year for copyright
            current_year = datetime.now().year

            # Get guild name if available
            guild_id = transcript.get('guild_id')
            guild_name = f"Guild ID: {guild_id}" if guild_id else "Discord Server"

            # Format timestamps
            created_at = transcript.get('created_at', 'Unknown')
            closed_at = transcript.get('closed_at', 'Unknown')

            # Try to parse and format dates if they're ISO format
            try:
                if created_at != 'Unknown':
                    created_dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    created_at = created_dt.strftime('%Y-%m-%d %H:%M:%S UTC')
            except:
                pass

            try:
                if closed_at != 'Unknown':
                    closed_dt = datetime.fromisoformat(closed_at.replace('Z', '+00:00'))
                    closed_at = closed_dt.strftime('%Y-%m-%d %H:%M:%S UTC')
            except:
                pass

            # Calculate resolution time if available (handle None values)
            resolution_time = transcript.get('resolution_time', 0)
            if resolution_time is None:
                resolution_time = 0
            resolution_str = "N/A"
            if resolution_time > 0:
                hours = int(resolution_time // 60)
                minutes = int(resolution_time % 60)
                resolution_str = f"{hours}h {minutes}m"

            # Get category name
            category = transcript.get('category', 'Support')

            html = [
                "<!DOCTYPE html>",
                "<html lang='en'>",
            "<head>",
            "    <meta charset='utf-8'>",
            "    <meta name='viewport' content='width=device-width, initial-scale=1'>",
            f"    <title>Ticket #{transcript.get('ticket_id', 'Unknown')} - Transcript</title>",
            "    <style>",
            "        :root {",
            "            --primary-color: #5865F2;",
            "            --secondary-color: #2b2d31;",
            "            --accent-color: #5865F2;",
            "            --text-color: #2e3338;",
            "            --light-text: #6c757d;",
            "            --border-color: #e9ecef;",
            "            --bg-light: #f8f9fa;",
            "            --bg-dark: #2b2d31;",
            "        }",
            "        * { box-sizing: border-box; }",
            "        body {",
            "            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;",
            "            line-height: 1.6;",
            "            color: var(--text-color);",
            "            max-width: 900px;",
            "            margin: 0 auto;",
            "            padding: 20px;",
            "            background-color: #ffffff;",
            "        }",
            "        .container { box-shadow: 0 0 20px rgba(0,0,0,0.1); border-radius: 8px; overflow: hidden; }",
            "        .header {",
            "            background-color: var(--secondary-color);",
            "            color: white;",
            "            padding: 20px;",
            "            border-bottom: 1px solid var(--border-color);",
            "        }",
            "        .header h1 {",
            "            margin: 0 0 10px 0;",
            "            font-size: 24px;",
            "            font-weight: 600;",
            "        }",
            "        .header-meta {",
            "            display: flex;",
            "            flex-wrap: wrap;",
            "            gap: 20px;",
            "            margin-top: 15px;",
            "        }",
            "        .meta-item {",
            "            flex: 1;",
            "            min-width: 200px;",
            "        }",
            "        .meta-label {",
            "            font-size: 12px;",
            "            text-transform: uppercase;",
            "            color: rgba(255,255,255,0.7);",
            "            margin-bottom: 4px;",
            "        }",
            "        .meta-value {",
            "            font-size: 14px;",
            "            font-weight: 500;",
            "        }",
            "        .ticket-info {",
            "            background-color: var(--bg-light);",
            "            padding: 15px 20px;",
            "            border-bottom: 1px solid var(--border-color);",
            "        }",
            "        .ticket-info h2 {",
            "            margin: 0 0 10px 0;",
            "            font-size: 18px;",
            "            color: var(--secondary-color);",
            "        }",
            "        .info-grid {",
            "            display: grid;",
            "            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));",
            "            gap: 15px;",
            "        }",
            "        .info-item {",
            "            margin-bottom: 10px;",
            "        }",
            "        .info-label {",
            "            font-size: 12px;",
            "            color: var(--light-text);",
            "            margin-bottom: 2px;",
            "        }",
            "        .info-value {",
            "            font-size: 14px;",
            "            font-weight: 500;",
            "        }",
            "        .messages-container {",
            "            padding: 20px;",
            "            background-color: white;",
            "        }",
            "        .message {",
            "            padding: 12px 15px;",
            "            margin-bottom: 15px;",
            "            border-radius: 8px;",
            "            background-color: #f8f9fa;",
            "            border-left: 3px solid #dee2e6;",
            "            position: relative;",
            "        }",
            "        .message.staff {",
            "            background-color: #f0f7ff;",
            "            border-left-color: var(--primary-color);",
            "        }",
            "        .message.user {",
            "            background-color: #f8f9fa;",
            "            border-left-color: #6c757d;",
            "        }",
            "        .message-header {",
            "            display: flex;",
            "            justify-content: space-between;",
            "            align-items: center;",
            "            margin-bottom: 8px;",
            "        }",
            "        .author {",
            "            font-weight: 600;",
            "            color: var(--secondary-color);",
            "        }",
            "        .staff-badge {",
            "            display: inline-block;",
            "            background-color: var(--primary-color);",
            "            color: white;",
            "            font-size: 10px;",
            "            padding: 2px 6px;",
            "            border-radius: 10px;",
            "            margin-left: 8px;",
            "            text-transform: uppercase;",
            "            font-weight: 600;",
            "        }",
            "        .timestamp {",
            "            color: var(--light-text);",
            "            font-size: 12px;",
            "        }",
            "        .content {",
            "            font-size: 14px;",
            "            line-height: 1.5;",
            "            white-space: pre-wrap;",
            "            word-break: break-word;",
            "        }",
            "        .attachments {",
            "            margin-top: 10px;",
            "            display: flex;",
            "            flex-wrap: wrap;",
            "            gap: 10px;",
            "        }",
            "        .attachment {",
            "            background-color: rgba(0,0,0,0.03);",
            "            border-radius: 4px;",
            "            padding: 8px;",
            "            max-width: 100%;",
            "        }",
            "        .attachment img {",
            "            max-width: 100%;",
            "            max-height: 300px;",
            "            border-radius: 4px;",
            "            display: block;",
            "        }",
            "        .attachment a {",
            "            color: var(--primary-color);",
            "            text-decoration: none;",
            "            display: flex;",
            "            align-items: center;",
            "            font-size: 13px;",
            "        }",
            "        .attachment a:hover {",
            "            text-decoration: underline;",
            "        }",
            "        .footer {",
            "            text-align: center;",
            "            padding: 20px;",
            "            font-size: 12px;",
            "            color: var(--light-text);",
            "            border-top: 1px solid var(--border-color);",
            "        }",
            "        .no-messages {",
            "            text-align: center;",
            "            padding: 40px 20px;",
            "            color: var(--light-text);",
            "            font-style: italic;",
            "        }",
            "        @media print {",
            "            body { max-width: 100%; padding: 0; }",
            "            .container { box-shadow: none; }",
            "        }",
            "    </style>",
            "</head>",
            "<body>",
            "    <div class='container'>",
            "        <div class='header'>",
            f"            <h1>Ticket #{transcript.get('ticket_id', 'Unknown')}</h1>",
            "            <div class='header-meta'>",
            "                <div class='meta-item'>",
            "                    <div class='meta-label'>Category</div>",
            f"                    <div class='meta-value'>{category}</div>",
            "                </div>",
            "                <div class='meta-item'>",
            "                    <div class='meta-label'>Created</div>",
            f"                    <div class='meta-value'>{created_at}</div>",
            "                </div>",
            "                <div class='meta-item'>",
            "                    <div class='meta-label'>Closed</div>",
            f"                    <div class='meta-value'>{closed_at}</div>",
            "                </div>",
            "                <div class='meta-item'>",
            "                    <div class='meta-label'>Resolution Time</div>",
            f"                    <div class='meta-value'>{resolution_str}</div>",
            "                </div>",
            "            </div>",
            "        </div>",
            "        <div class='ticket-info'>",
            "            <h2>Ticket Information</h2>",
            "            <div class='info-grid'>",
            "                <div class='info-item'>",
            "                    <div class='info-label'>Server</div>",
            f"                    <div class='info-value'>{guild_name}</div>",
            "                </div>",
            "                <div class='info-item'>",
            "                    <div class='info-label'>Created By</div>",
            f"                    <div class='info-value'>{transcript.get('created_by', 'Unknown')}</div>",
            "                </div>",
            "                <div class='info-item'>",
            "                    <div class='info-label'>Closed By</div>",
            f"                    <div class='info-value'>{transcript.get('closed_by', 'Unknown')}</div>",
            "                </div>",
            "                <div class='info-item'>",
            "                    <div class='info-label'>Close Reason</div>",
            f"                    <div class='info-value'>{transcript.get('close_reason', 'No reason provided')}</div>",
            "                </div>",
            "                <div class='info-item'>",
            "                    <div class='info-label'>Total Messages</div>",
            f"                    <div class='info-value'>{len(transcript.get('messages', []))}</div>",
            "                </div>",
            "                <div class='info-item'>",
            "                    <div class='info-label'>Participants</div>",
            f"                    <div class='info-value'>{len(transcript.get('participants', []))}</div>",
            "                </div>",
            "            </div>",
            "        </div>",
            "        <div class='messages-container'>"
        ]

            # Get staff participants for styling (ensure it's a set and handle None values)
            staff_participants_raw = transcript.get("staff_participants", [])
            if staff_participants_raw is None:
                staff_participants_raw = []
            staff_participants = set(staff_participants_raw)

            # Add messages
            messages = transcript.get("messages", [])
            if not messages:
                html.append("            <div class='no-messages'>No messages found in this ticket.</div>")
            else:
                for msg in messages:
                    timestamp = msg.get("timestamp", "")
                    author_id = msg.get("author_id")  # Don't default to 0, keep as None if not present
                    author_name = msg.get("author_name", "Unknown")
                    content = msg.get("content", "").replace("\n", "<br>")

                    # Try to parse and format timestamp
                    try:
                        if timestamp:
                            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                            timestamp = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass

                    # Determine if author is staff (handle None author_id with comprehensive error checking)
                    is_staff = False
                    try:
                        if author_id is not None and staff_participants is not None:
                            is_staff = author_id in staff_participants
                    except TypeError as e:
                        logger.error(f"TypeError in staff check: {e}, author_id={author_id}, staff_participants={staff_participants}")
                        is_staff = False
                    message_class = "staff" if is_staff else "user"

                    html.append(f"            <div class='message {message_class}'>")
                    html.append("                <div class='message-header'>")

                    # Add author with staff badge if applicable
                    if is_staff:
                        html.append(f"                    <div class='author'>{author_name}<span class='staff-badge'>Staff</span></div>")
                    else:
                        html.append(f"                    <div class='author'>{author_name}</div>")

                    html.append(f"                    <div class='timestamp'>{timestamp}</div>")
                    html.append("                </div>")
                    html.append(f"                <div class='content'>{content}</div>")

                    # Add attachments if any
                    attachments = msg.get("attachments", [])
                    if attachments:
                        html.append("                <div class='attachments'>")
                        for attachment in attachments:
                            if attachment.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
                                html.append(f"                    <div class='attachment'><img src='{attachment}' alt='Image Attachment'></div>")
                            else:
                                file_name = attachment.split('/')[-1]
                                html.append(f"                    <div class='attachment'><a href='{attachment}' target='_blank'>📎 {file_name}</a></div>")
                        html.append("                </div>")

                    html.append("            </div>")

            # Close containers and add footer
            html.append("        </div>")
            html.append("        <div class='footer'>")
            html.append(f"            <p>Ticket Transcript #{transcript.get('ticket_id', 'Unknown')} • Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} UTC</p>")
            html.append(f"            <p>© {current_year} Support System • All Rights Reserved</p>")
            html.append("        </div>")
            html.append("    </div>")
            html.append("</body>")
            html.append("</html>")

            return "\n".join(html)

        except Exception as e:
            logger.error(f"Error in HTML transcript formatting: {e}")
            traceback.print_exc()
            # Return a simple error message instead of failing completely
            return f"<html><body><h1>Error generating transcript</h1><p>Error: {str(e)}</p></body></html>"

    async def _format_transcript_pdf(self, transcript: Dict[str, Any]) -> Optional[bytes]:
        """Format transcript as PDF using HTML conversion"""
        try:
            # First generate HTML version
            html_content = self._format_transcript_html(transcript)

            # For now, we'll return the HTML content as bytes
            # In a production environment, you would use a library like weasyprint or a service
            # to convert HTML to PDF

            # Placeholder implementation - convert HTML to bytes
            pdf_bytes = html_content.encode('utf-8')

            # Add a header to indicate this is actually HTML content
            # In production, replace this with actual PDF conversion
            header = b"<!-- HTML content (PDF conversion not available) -->\n"
            return header + pdf_bytes

        except Exception as e:
            logger.error(f"Error generating PDF transcript: {e}")
            traceback.print_exc()
            return None

    def _calculate_resolution_time(self, created_at: str, closed_at: str) -> float:
        """Calculate resolution time in minutes"""
        if not created_at or not closed_at:
            return 0

        try:
            created = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            closed = datetime.fromisoformat(closed_at.replace('Z', '+00:00'))

            delta = closed - created
            return delta.total_seconds() / 60  # Return minutes
        except Exception:
            return 0

    def _calculate_first_response_time(self,
                                     messages: List[Dict[str, Any]],
                                     creator_id: int,
                                     staff_roles: List[int]) -> float:
        """Calculate time to first staff response in minutes"""
        if not messages or not creator_id:
            return 0

        try:
            # Find ticket creation time
            created_at = None
            for msg in messages:
                author_id = msg.get("author_id")
                if author_id is not None and author_id == creator_id:
                    created_at = msg.get("timestamp")
                    break

            if not created_at:
                return 0

            # Find first staff response
            first_response = None
            for msg in messages:
                # Skip messages from the creator
                author_id = msg.get("author_id")
                if author_id is not None and author_id == creator_id:
                    continue

                # Check if this is a staff message
                is_staff = False
                author_roles = msg.get("author_roles", [])

                # Ensure both role and staff_roles contain valid values for comparison
                if author_roles and staff_roles:
                    for role in author_roles:
                        if role is not None and role in staff_roles:
                            is_staff = True
                            break

                if is_staff:
                    first_response = msg.get("timestamp")
                    break

            if not first_response:
                return 0

            # Calculate time difference
            created = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            response = datetime.fromisoformat(first_response.replace('Z', '+00:00'))

            delta = response - created
            return delta.total_seconds() / 60  # Return minutes
        except Exception:
            return 0

    def _extract_participants(self, messages: List[Dict[str, Any]]) -> List[int]:
        """Extract unique participant IDs from messages"""
        participants = set()

        for msg in messages:
            author_id = msg.get("author_id")
            if author_id is not None:
                participants.add(author_id)

        return list(participants)

    def _extract_staff_participants(self,
                                  messages: List[Dict[str, Any]],
                                  staff_roles: List[int]) -> List[int]:
        """Extract staff participants based on roles"""
        staff = set()

        for msg in messages:
            author_id = msg.get("author_id")
            author_roles = msg.get("author_roles", [])

            if not author_id or not author_roles:
                continue

            # Check if author has a staff role (ensure valid values for comparison)
            if author_roles and staff_roles:
                for role in author_roles:
                    if role is not None and role in staff_roles:
                        staff.add(author_id)
                        break

        return list(staff)

    async def _update_statistics(self, transcript_data: Dict[str, Any]) -> None:
        """Update statistical aggregates for reporting"""
        try:
            # Extract date (YYYY-MM-DD) from closed_at
            closed_at = transcript_data.get("closed_at", "")
            if not closed_at:
                return

            date_str = closed_at.split("T")[0]
            guild_id = transcript_data.get("guild_id")
            category = transcript_data.get("category", "general")

            # Update daily stats
            await self.transcript_stats_collection.update_one(
                {
                    "date": date_str,
                    "guild_id": guild_id,
                    "category": category
                },
                {
                    "$inc": {
                        "ticket_count": 1,
                        "total_messages": transcript_data.get("message_count", 0),
                        "total_resolution_time": transcript_data.get("resolution_time", 0),
                        "total_first_response_time": transcript_data.get("first_response_time", 0)
                    },
                    "$set": {
                        "last_updated": datetime.now().isoformat()
                    }
                },
                upsert=True
            )

        except Exception as e:
            logger.error(f"Error updating statistics: {e}")

    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for monitoring"""
        metrics = {
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "cache_hit_ratio": self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
            "cache_size": len(self.cache),
            "operation_counts": dict(self.operation_counts),
            "error_counts": dict(self.error_counts),
            "average_times": {}
        }

        # Calculate average operation times
        for op, times in self.operation_times.items():
            if times:
                metrics["average_times"][op] = sum(times) / len(times)

        return metrics

# Create global instance
transcript_manager = TranscriptManager()

# Helper functions for backward compatibility
async def store_transcript(ticket_data, messages):
    """Store a transcript (compatibility function)"""
    return await transcript_manager.store_transcript(ticket_data, messages)

async def get_transcript(transcript_id=None, ticket_id=None):
    """Get a transcript (compatibility function)"""
    return await transcript_manager.get_transcript(transcript_id, ticket_id)

async def search_transcripts(**kwargs):
    """Search transcripts (compatibility function)"""
    return await transcript_manager.search_transcripts(**kwargs)

async def export_transcript(transcript_id=None, ticket_id=None, format="txt"):
    """Export a transcript (compatibility function)"""
    return await transcript_manager.export_transcript(transcript_id, ticket_id, format)

# Add the missing helper methods to the TranscriptManager class
def _get_enhanced_css():
    """Get the enhanced CSS for Discord-like styling"""
    return """
        :root {
            --discord-dark: #000000;
            --discord-darker: #1e1f22;
            --discord-darkest: #111214;
            --discord-blurple: #5865f2;
            --discord-green: #23a55a;
            --discord-red: #f23f42;
            --discord-yellow: #fee75c;
            --text-normal: #dbdee1;
            --text-muted: #b5bac1;
            --text-faint: #6d6f78;
            --interactive-normal: #b5bac1;
            --interactive-hover: #dcddde;
            --interactive-active: #ffffff;
            --background-primary: #000000;
            --background-secondary: #1e1f22;
            --background-tertiary: #111214;
            --background-accent: #4f545c;
            --background-floating: #18191c;
            --background-modifier-hover: rgba(79,84,92,0.16);
            --background-modifier-active: rgba(79,84,92,0.24);
            --background-modifier-selected: rgba(79,84,92,0.32);
            --background-modifier-accent: hsla(0,0%,100%,0.06);
            --elevation-high: 0 8px 16px hsla(0,0%,0%,0.24);
        }

        * { box-sizing: border-box; margin: 0; padding: 0; }

        body {
            font-family: 'gg sans', 'Noto Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.375;
            color: var(--text-normal);
            background-color: var(--background-primary);
            font-size: 16px;
            font-weight: 400;
            overflow-x: hidden;
            min-height: 100vh;
        }

        .transcript-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: var(--background-primary);
            box-shadow: var(--elevation-high);
            border-radius: 8px;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--discord-dark) 0%, var(--discord-darker) 100%);
            color: var(--text-normal);
            padding: 32px;
            border-bottom: 1px solid var(--background-modifier-accent);
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--discord-blurple), var(--discord-green));
        }

        .header h1 {
            margin: 0 0 16px 0;
            font-size: 32px;
            font-weight: 700;
            color: var(--interactive-active);
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
            margin-top: 20px;
        }

        .meta-item {
            background: var(--background-modifier-accent);
            padding: 16px;
            border-radius: 8px;
            border: 1px solid var(--background-modifier-selected);
        }

        .meta-label {
            font-size: 11px;
            text-transform: uppercase;
            color: var(--text-muted);
            margin-bottom: 6px;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .meta-value {
            font-size: 15px;
            font-weight: 600;
            color: var(--text-normal);
        }

        .ticket-info {
            background-color: var(--background-secondary);
            padding: 24px 32px;
            border-bottom: 1px solid var(--background-modifier-accent);
        }

        .ticket-info h2 {
            margin: 0 0 16px 0;
            font-size: 20px;
            color: var(--interactive-active);
            font-weight: 600;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .info-item {
            background: var(--background-modifier-accent);
            padding: 16px;
            border-radius: 6px;
            border: 1px solid var(--background-modifier-selected);
        }

        .info-label {
            font-size: 12px;
            color: var(--text-muted);
            margin-bottom: 4px;
            font-weight: 500;
        }

        .info-value {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-normal);
        }

        .events-section {
            background-color: var(--background-secondary);
            padding: 24px 32px;
            border-bottom: 1px solid var(--background-modifier-accent);
        }

        .events-section h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            color: var(--interactive-active);
            font-weight: 600;
        }

        .events-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .event {
            padding: 12px 16px;
            border-radius: 6px;
            font-size: 14px;
            background: var(--background-modifier-accent);
            border-left: 3px solid var(--discord-blurple);
        }

        .event.join {
            border-left-color: var(--discord-green);
        }

        .event.boost {
            border-left-color: var(--discord-yellow);
        }

        .messages-container {
            padding: 24px 32px;
            background-color: var(--background-primary);
        }

        .messages-container h3 {
            margin: 0 0 24px 0;
            font-size: 20px;
            color: var(--interactive-active);
            font-weight: 600;
        }

        .message-group {
            margin-bottom: 20px;
            display: flex;
            gap: 16px;
        }

        .message-group.staff {
            background: var(--background-modifier-accent);
            border-radius: 8px;
            padding: 16px;
            border-left: 4px solid var(--discord-blurple);
        }

        .message-group.bot {
            background: var(--background-modifier-accent);
            border-radius: 8px;
            padding: 16px;
            border-left: 4px solid var(--discord-yellow);
        }

        .message-author {
            flex-shrink: 0;
            width: 60px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .avatar-placeholder {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--background-accent);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .author-info {
            margin-top: 8px;
            text-align: center;
        }

        .author-name {
            font-weight: 600;
            font-size: 14px;
            color: var(--interactive-active);
            display: block;
        }

        .staff-badge, .bot-badge {
            display: inline-block;
            background-color: var(--discord-blurple);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            margin-top: 4px;
            text-transform: uppercase;
            font-weight: 600;
        }

        .bot-badge {
            background-color: var(--discord-yellow);
            color: var(--discord-dark);
        }

        .timestamp {
            color: var(--text-muted);
            font-size: 11px;
            margin-top: 4px;
        }

        .message-content {
            flex: 1;
            min-width: 0;
        }

        .message {
            margin-bottom: 8px;
        }

        .reply-reference {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            padding: 8px 12px;
            background: var(--background-modifier-hover);
            border-radius: 4px;
            border-left: 2px solid var(--text-muted);
            font-size: 13px;
        }

        .reply-icon {
            color: var(--text-muted);
        }

        .reply-author {
            font-weight: 600;
            color: var(--interactive-hover);
        }

        .reply-content {
            color: var(--text-muted);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .text-content {
            font-size: 16px;
            line-height: 1.375;
            color: var(--text-normal);
            word-wrap: break-word;
        }

        .text-content code.inline-code {
            background: var(--background-tertiary);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
        }

        .text-content pre {
            background: var(--background-tertiary);
            border-radius: 4px;
            padding: 12px;
            margin: 8px 0;
            overflow-x: auto;
        }

        .text-content pre code {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.125;
        }

        .mention {
            background: var(--discord-blurple);
            color: white;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 500;
        }

        .embed {
            border-left: 4px solid var(--background-accent);
            background: var(--background-secondary);
            border-radius: 4px;
            padding: 16px;
            margin: 8px 0;
            max-width: 520px;
        }

        .embed-author {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .embed-author-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }

        .embed-author-name {
            font-weight: 600;
            font-size: 14px;
        }

        .embed-title {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 8px;
        }

        .embed-title a {
            color: var(--discord-blurple);
            text-decoration: none;
        }

        .embed-description {
            font-size: 14px;
            line-height: 1.375;
            margin-bottom: 8px;
        }

        .embed-fields {
            display: grid;
            gap: 8px;
        }

        .embed-field.inline {
            display: inline-block;
            width: calc(33.33% - 8px);
            margin-right: 8px;
        }

        .embed-field-name {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 2px;
        }

        .embed-field-value {
            font-size: 14px;
            line-height: 1.375;
        }

        .embed-image, .embed-thumbnail {
            max-width: 100%;
            border-radius: 4px;
            margin: 8px 0;
        }

        .embed-thumbnail {
            max-width: 80px;
            float: right;
        }

        .embed-footer {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            font-size: 12px;
            color: var(--text-muted);
        }

        .embed-footer-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }

        .attachments {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin: 8px 0;
        }

        .attachment {
            background: var(--background-secondary);
            border-radius: 4px;
            padding: 8px;
            border: 1px solid var(--background-modifier-accent);
        }

        .attachment-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 4px;
            display: block;
        }

        .attachment-video, .attachment-audio {
            max-width: 100%;
            border-radius: 4px;
        }

        .attachment-file {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .attachment-link {
            display: flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            color: var(--text-normal);
        }

        .attachment-link:hover {
            color: var(--interactive-hover);
        }

        .attachment-icon {
            font-size: 24px;
        }

        .attachment-details {
            display: flex;
            flex-direction: column;
        }

        .attachment-name {
            font-weight: 500;
            font-size: 14px;
        }

        .attachment-size {
            font-size: 12px;
            color: var(--text-muted);
        }

        .attachment-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
        }

        .reactions {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            margin: 8px 0;
        }

        .reaction {
            background: var(--background-modifier-hover);
            border: 1px solid var(--background-modifier-accent);
            border-radius: 12px;
            padding: 4px 8px;
            font-size: 14px;
            cursor: default;
        }

        .reaction:hover {
            background: var(--background-modifier-active);
        }

        .edited-indicator {
            color: var(--text-muted);
            font-size: 11px;
            margin-left: 8px;
        }

        .message-timestamp {
            color: var(--text-muted);
            font-size: 11px;
            margin-top: 4px;
        }

        .search-container {
            background-color: var(--background-secondary);
            padding: 24px 32px;
            border-top: 1px solid var(--background-modifier-accent);
        }

        .search-container h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            color: var(--interactive-active);
            font-weight: 600;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px;
            background: var(--background-tertiary);
            border: 1px solid var(--background-modifier-accent);
            border-radius: 6px;
            color: var(--text-normal);
            font-size: 16px;
            margin-bottom: 16px;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--discord-blurple);
        }

        .search-filters {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 8px 16px;
            background: var(--background-modifier-accent);
            border: 1px solid var(--background-modifier-selected);
            border-radius: 6px;
            color: var(--text-normal);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-btn:hover {
            background: var(--background-modifier-hover);
        }

        .filter-btn.active {
            background: var(--discord-blurple);
            border-color: var(--discord-blurple);
            color: white;
        }

        .footer {
            text-align: center;
            padding: 32px;
            font-size: 12px;
            color: var(--text-muted);
            border-top: 1px solid var(--background-modifier-accent);
            background: var(--background-secondary);
        }

        .footer p {
            margin-bottom: 8px;
        }

        .features-used {
            margin-top: 16px;
            padding: 12px;
            background: var(--background-modifier-accent);
            border-radius: 6px;
            font-size: 11px;
        }

        .no-messages {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-muted);
            font-style: italic;
            font-size: 18px;
        }

        @media (max-width: 768px) {
            .transcript-container {
                margin: 0;
                border-radius: 0;
            }

            .header, .ticket-info, .messages-container, .search-container, .footer {
                padding: 16px;
            }

            .header-meta, .info-grid {
                grid-template-columns: 1fr;
            }

            .message-group {
                flex-direction: column;
                gap: 8px;
            }

            .message-author {
                flex-direction: row;
                width: auto;
                align-items: center;
                gap: 12px;
            }

            .author-info {
                text-align: left;
                margin-top: 0;
            }
        }

        @media print {
            body {
                background: white !important;
                color: black !important;
            }

            .transcript-container {
                box-shadow: none;
                max-width: 100%;
            }

            .search-container {
                display: none;
            }
        }
    """

def _get_enhanced_javascript():
    """Get the enhanced JavaScript for search and filtering functionality"""
    return """
        // Search functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            const messageGroups = document.querySelectorAll('.message-group');
            const filterBtns = document.querySelectorAll('.filter-btn');

            let currentFilter = 'all';

            // Search function
            function performSearch() {
                const query = searchInput.value.toLowerCase();

                messageGroups.forEach(group => {
                    const textContent = group.textContent.toLowerCase();
                    const matchesSearch = query === '' || textContent.includes(query);
                    const matchesFilter = applyFilter(group);

                    if (matchesSearch && matchesFilter) {
                        group.style.display = 'flex';
                        // Highlight search terms
                        if (query !== '') {
                            highlightText(group, query);
                        } else {
                            removeHighlights(group);
                        }
                    } else {
                        group.style.display = 'none';
                    }
                });
            }

            // Filter function
            function applyFilter(group) {
                if (currentFilter === 'all') return true;
                if (currentFilter === 'staff') return group.classList.contains('staff');
                if (currentFilter === 'user') return !group.classList.contains('staff') && !group.classList.contains('bot');
                return true;
            }

            // Highlight function
            function highlightText(element, query) {
                const textNodes = getTextNodes(element);
                textNodes.forEach(node => {
                    const text = node.textContent;
                    const regex = new RegExp(`(${escapeRegex(query)})`, 'gi');
                    if (regex.test(text)) {
                        const highlightedText = text.replace(regex, '<mark style="background: #fee75c; color: #000;">$1</mark>');
                        const wrapper = document.createElement('span');
                        wrapper.innerHTML = highlightedText;
                        node.parentNode.replaceChild(wrapper, node);
                    }
                });
            }

            // Remove highlights
            function removeHighlights(element) {
                const marks = element.querySelectorAll('mark');
                marks.forEach(mark => {
                    mark.outerHTML = mark.innerHTML;
                });
            }

            // Get text nodes
            function getTextNodes(element) {
                const textNodes = [];
                const walker = document.createTreeWalker(
                    element,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );

                let node;
                while (node = walker.nextNode()) {
                    if (node.textContent.trim() !== '') {
                        textNodes.push(node);
                    }
                }
                return textNodes;
            }

            // Escape regex
            function escapeRegex(string) {
                return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');
            }

            // Event listeners
            searchInput.addEventListener('input', performSearch);

            // Filter buttons
            window.filterByStaff = function() {
                currentFilter = 'staff';
                updateFilterButtons('staff');
                performSearch();
            };

            window.filterByUser = function() {
                currentFilter = 'user';
                updateFilterButtons('user');
                performSearch();
            };

            window.clearFilters = function() {
                currentFilter = 'all';
                searchInput.value = '';
                updateFilterButtons('all');
                performSearch();
            };

            function updateFilterButtons(active) {
                filterBtns.forEach(btn => {
                    btn.classList.remove('active');
                });

                if (active === 'staff') {
                    filterBtns[0].classList.add('active');
                } else if (active === 'user') {
                    filterBtns[1].classList.add('active');
                } else {
                    filterBtns[2].classList.add('active');
                }
            }

            // Smooth scrolling for internal links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Copy message functionality
            document.querySelectorAll('.message').forEach(message => {
                message.addEventListener('dblclick', function() {
                    const textContent = this.querySelector('.text-content');
                    if (textContent) {
                        navigator.clipboard.writeText(textContent.textContent).then(() => {
                            // Show brief feedback
                            const feedback = document.createElement('div');
                            feedback.textContent = 'Copied!';
                            feedback.style.cssText = `
                                position: fixed;
                                top: 20px;
                                right: 20px;
                                background: var(--discord-green);
                                color: white;
                                padding: 8px 16px;
                                border-radius: 4px;
                                z-index: 1000;
                                font-size: 14px;
                            `;
                            document.body.appendChild(feedback);
                            setTimeout(() => feedback.remove(), 2000);
                        });
                    }
                });
            });
        });
    """

# Add the missing methods to the TranscriptManager class
TranscriptManager._get_enhanced_css = _get_enhanced_css
TranscriptManager._get_enhanced_javascript = _get_enhanced_javascript

# Add the missing helper methods for message processing
def _group_messages_by_user(self, messages):
    """Group consecutive messages by the same user for better visual clustering"""
    if not messages:
        return []

    groups = []
    current_group = [messages[0]]

    for i in range(1, len(messages)):
        current_msg = messages[i]
        prev_msg = messages[i-1]

        # Check if messages should be grouped (same author, within 5 minutes)
        same_author = current_msg.get('author_id') == prev_msg.get('author_id')

        time_diff = 0
        try:
            current_time = datetime.fromisoformat(current_msg.get('timestamp', '').replace('Z', '+00:00'))
            prev_time = datetime.fromisoformat(prev_msg.get('timestamp', '').replace('Z', '+00:00'))
            time_diff = (current_time - prev_time).total_seconds()
        except:
            pass

        if same_author and time_diff < 300:  # 5 minutes
            current_group.append(current_msg)
        else:
            groups.append(current_group)
            current_group = [current_msg]

    groups.append(current_group)
    return groups

def _format_message_group(self, group, staff_participants):
    """Format a group of messages from the same user"""
    if not group:
        return []

    html = []
    first_msg = group[0]

    # Determine if author is staff
    author_id = first_msg.get("author_id")
    is_staff = author_id in staff_participants if author_id else False
    is_bot = first_msg.get("is_bot", False)

    # Get author info
    author_name = first_msg.get("author_name", "Unknown")
    author_avatar = first_msg.get("author_avatar", "")
    role_color = first_msg.get("role_color", "")

    # Start message group
    group_class = "staff" if is_staff else "bot" if is_bot else "user"
    html.append(f"            <div class='message-group {group_class}' data-author-id='{author_id}'>")

    # Add avatar and author info
    html.append("                <div class='message-author'>")
    if author_avatar:
        html.append(f"                    <img src='{author_avatar}' alt='{author_name}' class='avatar'>")
    else:
        html.append("                    <div class='avatar-placeholder'>👤</div>")

    html.append("                    <div class='author-info'>")
    author_style = f"color: {role_color};" if role_color else ""
    html.append(f"                        <span class='author-name' style='{author_style}'>{author_name}</span>")

    if is_staff:
        html.append("                        <span class='staff-badge'>STAFF</span>")
    elif is_bot:
        html.append("                        <span class='bot-badge'>BOT</span>")

    # Add timestamp for first message
    timestamp = first_msg.get("timestamp_formatted", "")
    html.append(f"                        <span class='timestamp'>{timestamp}</span>")
    html.append("                    </div>")
    html.append("                </div>")

    # Add all messages in the group
    html.append("                <div class='message-content'>")
    for msg in group:
        html.extend(self._format_single_message(msg, is_first=(msg == first_msg)))
    html.append("                </div>")

    html.append("            </div>")
    return html

def _format_single_message(self, msg, is_first=False):
    """Format a single message within a group"""
    html = []

    # Message container
    msg_id = msg.get("id", "")
    html.append(f"                    <div class='message' data-message-id='{msg_id}'>")

    # Add reply reference if exists
    reference = msg.get("reference")
    if reference:
        html.append("                        <div class='reply-reference'>")
        html.append(f"                            <span class='reply-icon'>↳</span>")
        html.append(f"                            <span class='reply-author'>{reference.get('author_name', 'Unknown')}</span>")
        html.append(f"                            <span class='reply-content'>{reference.get('content', '[Message not found]')}</span>")
        html.append("                        </div>")

    # Message content
    content = msg.get("content", "")
    if content:
        # Process content for code blocks and mentions
        processed_content = self._process_message_content(content)
        html.append(f"                        <div class='text-content'>{processed_content}</div>")

    # Add embeds
    embeds = msg.get("embeds", [])
    for embed in embeds:
        html.extend(self._format_embed(embed))

    # Add attachments
    attachments = msg.get("attachments", [])
    if attachments:
        html.append("                        <div class='attachments'>")
        for attachment in attachments:
            html.extend(self._format_attachment(attachment))
        html.append("                        </div>")

    # Add reactions
    reactions = msg.get("reactions", [])
    if reactions:
        html.append("                        <div class='reactions'>")
        for reaction in reactions:
            emoji = reaction.get("emoji", "")
            count = reaction.get("count", 0)
            users = reaction.get("users", [])
            user_list = ", ".join([user.get("name", "Unknown") for user in users[:5]])
            if len(users) > 5:
                user_list += f" and {len(users) - 5} more"

            html.append(f"                            <span class='reaction' title='{user_list}'>{emoji} {count}</span>")
        html.append("                        </div>")

    # Add edit indicator
    edited_at = msg.get("edited_at")
    if edited_at:
        html.append("                        <span class='edited-indicator'>(edited)</span>")

    # Add timestamp for non-first messages in group
    if not is_first:
        timestamp = msg.get("timestamp_formatted", "")
        html.append(f"                        <span class='message-timestamp'>{timestamp}</span>")

    html.append("                    </div>")
    return html

def _process_message_content(self, content):
    """Process message content for code blocks, mentions, etc."""
    import re

    # Replace code blocks with syntax highlighting
    def replace_code_block(match):
        language = match.group(1) or ""
        code = match.group(2)
        return f'<pre><code class="language-{language}">{code}</code></pre>'

    # Replace inline code
    def replace_inline_code(match):
        code = match.group(1)
        return f'<code class="inline-code">{code}</code>'

    # Replace user mentions
    def replace_mentions(match):
        user_id = match.group(1)
        return f'<span class="mention">@User</span>'

    # Replace channel mentions
    def replace_channel_mentions(match):
        channel_id = match.group(1)
        return f'<span class="mention">#channel</span>'

    # Replace role mentions
    def replace_role_mentions(match):
        role_id = match.group(1)
        return f'<span class="mention">@role</span>'

    # Apply replacements
    content = re.sub(r'```(\w+)?\n(.*?)\n```', replace_code_block, content, flags=re.DOTALL)
    content = re.sub(r'`([^`]+)`', replace_inline_code, content)
    content = re.sub(r'<@!?(\d+)>', replace_mentions, content)
    content = re.sub(r'<#(\d+)>', replace_channel_mentions, content)
    content = re.sub(r'<@&(\d+)>', replace_role_mentions, content)

    # Replace newlines with <br>
    content = content.replace('\n', '<br>')

    return content

def _format_embed(self, embed):
    """Format a Discord embed"""
    html = []

    embed_color = embed.get("color")
    color_style = f"border-left-color: #{embed_color:06x};" if embed_color else ""

    html.append(f"                        <div class='embed' style='{color_style}'>")

    # Embed author
    author = embed.get("author")
    if author:
        html.append("                            <div class='embed-author'>")
        if author.get("icon_url"):
            html.append(f"                                <img src='{author['icon_url']}' class='embed-author-icon'>")
        html.append(f"                                <span class='embed-author-name'>{author.get('name', '')}</span>")
        html.append("                            </div>")

    # Embed title
    title = embed.get("title")
    if title:
        if embed.get("url"):
            html.append(f"                            <div class='embed-title'><a href='{embed['url']}' target='_blank'>{title}</a></div>")
        else:
            html.append(f"                            <div class='embed-title'>{title}</div>")

    # Embed description
    description = embed.get("description")
    if description:
        html.append(f"                            <div class='embed-description'>{description}</div>")

    # Embed fields
    fields = embed.get("fields", [])
    if fields:
        html.append("                            <div class='embed-fields'>")
        for field in fields:
            field_class = "inline" if field.get("inline") else "block"
            html.append(f"                                <div class='embed-field {field_class}'>")
            html.append(f"                                    <div class='embed-field-name'>{field.get('name', '')}</div>")
            html.append(f"                                    <div class='embed-field-value'>{field.get('value', '')}</div>")
            html.append("                                </div>")
        html.append("                            </div>")

    # Embed image
    image = embed.get("image")
    if image:
        html.append(f"                            <img src='{image}' class='embed-image' alt='Embed Image'>")

    # Embed thumbnail
    thumbnail = embed.get("thumbnail")
    if thumbnail:
        html.append(f"                            <img src='{thumbnail}' class='embed-thumbnail' alt='Embed Thumbnail'>")

    # Embed footer
    footer = embed.get("footer")
    if footer:
        html.append("                            <div class='embed-footer'>")
        if footer.get("icon_url"):
            html.append(f"                                <img src='{footer['icon_url']}' class='embed-footer-icon'>")
        html.append(f"                                <span class='embed-footer-text'>{footer.get('text', '')}</span>")
        html.append("                            </div>")

    html.append("                        </div>")
    return html

def _format_attachment(self, attachment):
    """Format an attachment with enhanced metadata"""
    html = []

    filename = attachment.get("filename", "Unknown")
    url = attachment.get("url", "")
    file_type = attachment.get("file_type", "file")
    file_size = attachment.get("size", 0)

    # Format file size
    if file_size > 1024 * 1024:
        size_str = f"{file_size / (1024 * 1024):.1f} MB"
    elif file_size > 1024:
        size_str = f"{file_size / 1024:.1f} KB"
    else:
        size_str = f"{file_size} bytes"

    html.append("                            <div class='attachment'>")

    if file_type == "image":
        html.append(f"                                <img src='{url}' alt='{filename}' class='attachment-image'>")
        html.append(f"                                <div class='attachment-info'>")
        html.append(f"                                    <span class='attachment-name'>🖼️ {filename}</span>")
        html.append(f"                                    <span class='attachment-size'>{size_str}</span>")
        html.append(f"                                </div>")
    elif file_type == "video":
        html.append(f"                                <video controls class='attachment-video'>")
        html.append(f"                                    <source src='{url}' type='video/mp4'>")
        html.append(f"                                    Your browser does not support the video tag.")
        html.append(f"                                </video>")
        html.append(f"                                <div class='attachment-info'>")
        html.append(f"                                    <span class='attachment-name'>🎥 {filename}</span>")
        html.append(f"                                    <span class='attachment-size'>{size_str}</span>")
        html.append(f"                                </div>")
    elif file_type == "audio":
        html.append(f"                                <audio controls class='attachment-audio'>")
        html.append(f"                                    <source src='{url}' type='audio/mpeg'>")
        html.append(f"                                    Your browser does not support the audio tag.")
        html.append(f"                                </audio>")
        html.append(f"                                <div class='attachment-info'>")
        html.append(f"                                    <span class='attachment-name'>🎵 {filename}</span>")
        html.append(f"                                    <span class='attachment-size'>{size_str}</span>")
        html.append(f"                                </div>")
    else:
        # File type icons
        icon = "📄" if file_type == "document" else "📦" if file_type == "archive" else "📎"
        html.append(f"                                <div class='attachment-file'>")
        html.append(f"                                    <a href='{url}' target='_blank' class='attachment-link'>")
        html.append(f"                                        <span class='attachment-icon'>{icon}</span>")
        html.append(f"                                        <div class='attachment-details'>")
        html.append(f"                                            <span class='attachment-name'>{filename}</span>")
        html.append(f"                                            <span class='attachment-size'>{size_str}</span>")
        html.append(f"                                        </div>")
        html.append(f"                                    </a>")
        html.append(f"                                </div>")

    html.append("                            </div>")
    return html

# Add all the helper methods to the TranscriptManager class
TranscriptManager._group_messages_by_user = _group_messages_by_user
TranscriptManager._format_message_group = _format_message_group
TranscriptManager._format_single_message = _format_single_message
TranscriptManager._process_message_content = _process_message_content
TranscriptManager._format_embed = _format_embed
TranscriptManager._format_attachment = _format_attachment
