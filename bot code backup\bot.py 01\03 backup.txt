import os
import json
import logging
import asyncio
import aiohttp
import traceback
import threading
import requests
import random
import time
import pymongo
from datetime import datetime, timezone, timedelta
import discord
from discord.ext import commands, tasks
from discord import app_commands
from discord.ui import Select, View, Button, Modal, TextInput
from discord.utils import get
from colorama import Fore, init
from functools import partial
from bot_instance import bot  # Import bot from bot_instance
from tickets import (
    ticket_config, active_tickets, TicketView, set_staff_role,
    set_transcript_channel, close_ticket, create_ticket, load_ticket_data,
    save_ticket_data, add_category, set_ticket_channel, create_ticket_panel,
    reopen_ticket
)
from database import save_data, load_data, save_transaction, get_transactions, TRANSACTIONS_COLLECTION, get_guild_settings, save_guild_settings

def log_permission_check(interaction: discord.Interaction, command_name: str):
    """Log permission check details for commands"""
    user = interaction.user
    guild = interaction.guild
    has_admin = user.guild_permissions.administrator
    logging.info(f"Permission Check - Command: {command_name}")
    logging.info(f"User: {user.name}#{user.discriminator} (ID: {user.id})")
    logging.info(f"Guild: {guild.name} (ID: {guild.id})")
    logging.info(f"Has Admin Permission: {has_admin}")
    return has_admin

class RateLimitHandler:
    def __init__(self):
        self.rate_limits = {}
        self.queues = {}
        self.processing = {}
        self.max_retries = 10  # Increased from 3
        self.base_delay = 0.5
        self.max_delay = 15.0
        self.bulk_queue = asyncio.Queue()
        self.bulk_processing = False
        
    async def execute(self, key, coroutine, *args, **kwargs):
        """Execute a coroutine with enhanced rate limit handling"""
        if key not in self.queues:
            self.queues[key] = asyncio.Queue()
            self.processing[key] = False
            
        # Add to queue
        await self.queues[key].put((coroutine, args, kwargs))
        
        # Start processing if not already running
        if not self.processing[key]:
            self.processing[key] = True
            asyncio.create_task(self._process_queue(key))
            
    async def execute_bulk(self, operations):
        """Handle bulk operations more efficiently"""
        for op in operations:
            await self.bulk_queue.put(op)
            
        if not self.bulk_processing:
            self.bulk_processing = True
            asyncio.create_task(self._process_bulk_queue())
            
    async def _process_bulk_queue(self):
        """Process bulk operations with smart rate limiting"""
        try:
            batch_size = 0
            last_operation_time = time.time()
            
            while not self.bulk_queue.empty():
                current_time = time.time()
                time_diff = current_time - last_operation_time
                
                # Adjust batch size based on rate limit encounters
                if time_diff > 5:  # Reset batch size after 5 seconds of no rate limits
                    batch_size = min(50, batch_size + 10)
                
                # Process a batch of operations
                operations = []
                for _ in range(batch_size):
                    if self.bulk_queue.empty():
                        break
                    operations.append(await self.bulk_queue.get())
                
                try:
                    # Execute batch with retry logic
                    success = await self._execute_with_backoff(operations)
                    if success:
                        last_operation_time = time.time()
                    else:
                        batch_size = max(1, batch_size // 2)  # Reduce batch size on failure
                        
                except Exception as e:
                    print(f"Error in bulk processing: {e}")
                    batch_size = max(1, batch_size // 2)
                
                # Add small delay between batches
                await asyncio.sleep(0.1)
                
        finally:
            self.bulk_processing = False
            
    async def _process_queue(self, key):
        """Process queued items with enhanced rate limiting"""
        try:
            while not self.queues[key].empty():
                # Apply rate limit with jitter
                if key in self.rate_limits:
                    wait_time = self.rate_limits[key] - time.time()
                    if wait_time > 0:
                        jitter = random.uniform(0, 0.1 * wait_time)
                        await asyncio.sleep(wait_time + jitter)
                        
                # Get next item
                coroutine, args, kwargs = await self.queues[key].get()
                
                try:
                    # Execute with improved retry logic
                    for attempt in range(self.max_retries):
                        try:
                            await coroutine(*args, **kwargs)
                            break
                        except discord.HTTPException as e:
                            if e.status == 429:  # Rate limit
                                retry_after = e.retry_after
                                self.rate_limits[key] = time.time() + retry_after
                                # Calculate exponential backoff with jitter
                                backoff = min(self.max_delay, self.base_delay * (2 ** attempt))
                                jitter = random.uniform(0, 0.1 * backoff)
                                await asyncio.sleep(backoff + jitter)
                                continue
                            raise
                except Exception as e:
                    print(f"Error processing {key}: {e}")
                    
                # Dynamic delay between operations
                await asyncio.sleep(random.uniform(0.1, 0.3))
                
        finally:
            self.processing[key] = False
            
    async def _execute_with_backoff(self, operations):
        """Execute operations with exponential backoff"""
        for attempt in range(self.max_retries):
            try:
                for op in operations:
                    await op()
                return True
            except discord.HTTPException as e:
                if e.status == 429:
                    backoff = min(self.max_delay, self.base_delay * (2 ** attempt))
                    jitter = random.uniform(0, 0.1 * backoff)
                    await asyncio.sleep(backoff + jitter)
                    continue
                raise
        return False

# Create global rate limit handler
rate_limiter = RateLimitHandler()


init()


# Initialize file locks
file_locks = {}

# Initialize all global variables at the top level
gang_strikes = {}
gang_roles = {}
gang_members = {}
gang_leaders = {}
applications_status = {}
reaction_roles = {}
reaction_message_id = None
reaction_channel_id = None
application_forms = {}
application_channel = None
application_log_channel = None
sticky_messages = {}
welcome_channel_id = None
welcome_message = "Welcome!"
welcome_image_url = None
vanity_url = None
role_name = None
notification_channel_id = None
join_role_id = None
tebex_channel = None
webhook_url = None

def get_file_lock(file_path):
    """Get or create a lock for a specific file"""
    if file_path not in file_locks:
        file_locks[file_path] = threading.Lock()
    return file_locks[file_path]

def load_json(file_path):
    """Thread-safe JSON loading"""
    with get_file_lock(file_path):
        try:
            if not os.path.exists(file_path):
                with open(file_path, 'w') as f:
                    json.dump({}, f)
                return {}
            
            with open(file_path, 'r') as f:
                content = f.read()
                if not content:
                    return {}
                return json.loads(content)
                
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
            return {}
        except IOError as e:
            print(f"IO error: {e}")
            return {}
        except Exception as e:
            print(f"Error loading JSON file: {e}")
            return {}


def save_json(file_path, data):
    """Thread-safe JSON saving"""
    with get_file_lock(file_path):
        try:
            # Create temp file
            temp_path = f"{file_path}.tmp"
            with open(temp_path, 'w') as f:
                json.dump(data, f, indent=4)
            
            # Atomic rename
            os.replace(temp_path, file_path)
            return True
        except Exception as e:
            print(f"Error saving JSON file: {e}")
            return False

init()

async def delete_message(message):
    """Delete a message with enhanced rate limit handling"""
    try:
        await rate_limiter.execute(
            'delete_message',
            message.delete,
            reason="Bulk operation"
        )
        return True
    except Exception as e:
        print(f"Error deleting message: {e}")
        return False

async def delete_messages_bulk(messages):
    """Delete multiple messages efficiently"""
    try:
        operations = [
            lambda m=msg: m.delete(reason="Bulk operation")
            for msg in messages
        ]
        await rate_limiter.execute_bulk(operations)
        return True
    except Exception as e:
        print(f"Error in bulk message deletion: {e}")
        return False



# Initialize file locks
file_locks = {}

# Initialize all global variables at the top level
gang_strikes = {}
gang_roles = {}
gang_members = {}
gang_leaders = {}
applications_status = {}
reaction_roles = {}
reaction_message_id = None
reaction_channel_id = None
application_forms = {}
application_channel = None
application_log_channel = None
sticky_messages = {}
welcome_channel_id = None
welcome_message = "Welcome!"
welcome_image_url = None
vanity_url = None
role_name = None
notification_channel_id = None
join_role_id = None
tebex_channel = None
webhook_url = None

# Set up logging
logging.basicConfig(
    filename='bot.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Get the directory of the current script
script_dir = os.path.dirname(os.path.abspath(__file__))

# Define file path for data storage
DATA_FILE_PATH = r"F:\MissMinutesBotmain\bot_data.json"  # Path to the data file

# Load configuration
settings_path = os.path.join(script_dir, 'Settings.json')

# Check if the settings file exists
if not os.path.exists(settings_path):
    print(f"Error: Settings file not found at {settings_path}.")
    exit(1)

# Load configuration
with open(settings_path) as f:
    Settings = json.load(f)

# Get the token and prefix
token = Settings.get('Token')
prefix = Settings.get("Prefix")

# Check if the token is loaded correctly
if token is None:
    print("Error: Token is not set in the Settings.json file.")
    exit(1)  # Exit the program if the token is not found

# Initialize applications_status
applications_status = {}

# Use the existing command tree associated with the bot
tree = bot.tree


# Utility Functions
async def find_role_by_name(guild, role_name):
    for role in guild.roles:
        if role.name == role_name:
            return role
    return None

async def send_embed(channel, title, description, color=discord.Color.blue()):
    embed = discord.Embed(title=title, description=description, color=color)
    await channel.send(embed=embed)

# Vanity Role System
class Status:
    roles = 0

def safePrint(member=None, action=None, vocab=None, color=None):
    timestamp = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
    if vocab is not None:
        print(f"[{timestamp}] {member} [{color}{action} {vanity_url} {vocab} status]")
    else:
        print(f"[{timestamp}] {member} [{color}{action}]")

@bot.event
async def on_guild_join(guild):
    """Event handler for when the bot joins a new guild"""
    try:
        # Initialize default settings for the new guild
        default_settings = {
            "guild_id": guild.id,
            "vanity": {
                "url": None,
                "role_name": None
            },
            "notification_channel_id": None
        }
        
        # Save the default settings to database
        save_guild_settings(guild.id, default_settings)
        print(f"Bot joined new guild: {guild.name} (ID: {guild.id})")
    except Exception as e:
        print(f"Error initializing guild settings: {e}")

@tasks.loop(seconds=10)  # Check every 10 seconds for more frequent updates
async def check_vanity_status():
    """Check vanity status across all guilds the bot is in"""
    for guild in bot.guilds:
        try:
            # Get guild-specific settings
            settings = await get_guild_settings(guild.id)
            if not settings:
                # Initialize guild settings if they don't exist
                settings = {
                    "guild_id": guild.id,
                    "vanity": {},
                    "notification_channel_id": None
                }
                await save_guild_settings(guild.id, settings)
                continue

            # Check if vanity settings exist
            vanity_settings = settings.get("vanity", {})
            if not vanity_settings.get("url") or not vanity_settings.get("role_name"):
                continue

            vanity_url = vanity_settings["url"]
            role_name = vanity_settings["role_name"]
            notification_channel_id = settings.get("notification_channel_id")
            embed_title = vanity_settings.get("embed_title", "Priority Queue Granted")

            # Find the role
            role = await find_role_by_name(guild, role_name)
            if not role:
                print(f"Role '{role_name}' not found in guild {guild.name}")
                continue

            # Get notification channel
            notification_channel = guild.get_channel(notification_channel_id) if notification_channel_id else None

            for member in guild.members:
                if member.bot:
                    continue
                # Check if the member is online, idle, or do not disturb
                if member.status in (discord.Status.online, discord.Status.idle, discord.Status.dnd):
                    has_vanity_status = any(activity.name == vanity_url for activity in member.activities if isinstance(activity, discord.CustomActivity))
                    
                    if has_vanity_status and role not in member.roles:
                        await member.add_roles(role)
                        if notification_channel:
                            embed = discord.Embed(
                                title=embed_title,
                                description=vanity_settings.get("notification_message", "User just added '{status}' as their custom Discord status and received free priority queue! If you want it too, add '{status}' as your custom Discord status.").format(status=vanity_url),
                                color=discord.Color.green()
                            )
                            await notification_channel.send(content=member.mention, embed=embed)
                        safePrint(member, "Added role", "to", Fore.GREEN)
                    elif not has_vanity_status and role in member.roles:
                        await member.remove_roles(role)
                        if notification_channel:
                            embed = discord.Embed(
                                title="Priority Queue Removed",
                                description=f"User removed '{vanity_url}' from their custom Discord status and lost priority queue. Add '{vanity_url}' as your custom Discord status to get priority queue!",
                                color=discord.Color.red()
                            )
                            await notification_channel.send(content=member.mention, embed=embed)
                        safePrint(member, "Removed role", "from", Fore.RED)
        except Exception as e:
            print(f"Error checking vanity status in guild {guild.name}: {e}")

@tree.command(name="setup_tickets", description="Setup the ticket system for the server")
@app_commands.default_permissions(administrator=True)
async def setup_tickets_slash(interaction: discord.Interaction):
    try:
        await interaction.response.defer(ephemeral=True)
        
        embed = discord.Embed(
            title="🎫 Ticket System Setup",
            description="Select an option to manage the ticket system:",
            color=discord.Color.blue()
        )
        
        view = View()
        add_category_btn = Button(label="Add Category", style=discord.ButtonStyle.green, custom_id="add_category")
        set_staff_btn = Button(label="Set Staff Role", style=discord.ButtonStyle.blurple, custom_id="set_staff")
        set_transcript_btn = Button(label="Set Transcript Channel", style=discord.ButtonStyle.gray, custom_id="set_transcript")
        set_ticket_channel_btn = Button(label="Set Ticket Channel", style=discord.ButtonStyle.blurple, custom_id="set_ticket_channel")
        remove_category_btn = Button(label="Remove Category", style=discord.ButtonStyle.red, custom_id="remove_category")
        
        async def remove_category_callback(i: discord.Interaction):
            # Check if there are any categories first
            if not ticket_config.get("categories"):
                await i.response.send_message("No ticket categories exist to remove.", ephemeral=True)
                return
                
            # Create dropdown with existing categories
            options = [
                discord.SelectOption(
                    label=details["name"],
                    value=str(category_id),  # Ensure category_id is string
                    description=details.get("description", "")[:100]
                )
                for category_id, details in ticket_config["categories"].items()
            ]
            
            # Verify we have options before creating the dropdown
            if not options:
                await i.response.send_message("No ticket categories exist to remove.", ephemeral=True)
                return
                
            select = Select(
                placeholder="Select category to remove",
                options=options
            )
            
            async def select_callback(interaction: discord.Interaction):
                try:
                    # Defer the response first
                    await interaction.response.defer(ephemeral=True)
                    
                    selected_id = select.values[0]  # Get selected value as string
                    
                    # Check if category exists in config
                    if selected_id not in ticket_config["categories"]:
                        await interaction.followup.send("Category not found.", ephemeral=True)
                        return
                        
                    # Get the category from Discord using int conversion only when needed
                    category = interaction.guild.get_channel(int(selected_id))
                    if category:
                        try:
                            # Delete the category and its channels
                            for channel in category.channels:
                                await channel.delete()
                            await category.delete()
                        except discord.Forbidden:
                            await interaction.followup.send("Missing permissions to delete category.", ephemeral=True)
                            return
                        except Exception as e:
                            await interaction.followup.send(f"Error deleting category: {str(e)}", ephemeral=True)
                            return
                    
                    # Remove from config using string ID
                    del ticket_config["categories"][selected_id]
                    await save_ticket_data()
                    
                    # Update ticket panel if it exists
                    if ticket_config.get("ticket_channel"):
                        channel = interaction.guild.get_channel(ticket_config["ticket_channel"])
                        if channel:
                            await create_ticket_panel(channel)
                            
                    await interaction.followup.send("Category removed successfully!", ephemeral=True)
                    
                except ValueError:
                    await interaction.followup.send("Invalid category ID format.", ephemeral=True)
                except Exception as e:
                    await interaction.followup.send(f"An error occurred: {str(e)}", ephemeral=True)
            
            select.callback = select_callback
            
            # Create a new view for the dropdown
            dropdown_view = View()
            dropdown_view.add_item(select)
            
            await i.response.send_message("Select a category to remove:", view=dropdown_view, ephemeral=True)
        
        async def add_category_callback(i: discord.Interaction):
            modal = discord.ui.Modal(title="Add Ticket Category")
            modal.add_item(discord.ui.TextInput(label="Category Name", placeholder="e.g., Support"))
            modal.add_item(discord.ui.TextInput(label="Description", placeholder="Category description"))
            
            async def modal_callback(m_i: discord.Interaction):
                try:
                    name = modal.children[0].value
                    desc = modal.children[1].value
                    
                    # Create category
                    success, result = await add_category(m_i.guild, name, desc)
                    try:
                        if success:
                            try:
                                await m_i.response.send_message(f"Category '{name}' created successfully!", ephemeral=True)
                            except discord.errors.InteractionResponded:
                                await m_i.followup.send(f"Category '{name}' created successfully!", ephemeral=True)
                            
                            # Refresh ticket panel if it exists
                            if ticket_config.get("ticket_channel"):
                                channel = m_i.guild.get_channel(ticket_config["ticket_channel"])
                                if channel:
                                    # Clear existing messages
                                    async for message in channel.history(limit=100):
                                        if message.author == bot.user:
                                            await message.delete()
                                    # Create new panel
                                    await create_ticket_panel(channel)
                        else:
                            try:
                                await m_i.response.send_message(f"Error creating category: {result}", ephemeral=True)
                            except discord.errors.InteractionResponded:
                                await m_i.followup.send(f"Error creating category: {result}", ephemeral=True)
                    except discord.errors.NotFound:
                        print(f"Interaction expired while creating category {name}")
                except Exception as e:
                    print(f"Error in modal callback: {e}")
                    try:
                        await m_i.response.send_message("An error occurred while creating the category.", ephemeral=True)
                    except (discord.errors.InteractionResponded, discord.errors.NotFound):
                        try:
                            await m_i.followup.send("An error occurred while creating the category.", ephemeral=True)
                        except:
                            print("Could not send error message")
            
            modal.on_submit = modal_callback
            await i.response.send_modal(modal)
        
        async def set_staff_callback(i: discord.Interaction):
            modal = discord.ui.Modal(title="Set Staff Role")
            modal.add_item(discord.ui.TextInput(label="Role ID", placeholder="Enter role ID"))
            
            async def modal_callback(m_i: discord.Interaction):
                try:
                    role_id = int(modal.children[0].value)
                    success = await set_staff_role(role_id)
                    # Use response.defer() first to prevent timeout
                    await m_i.response.defer(ephemeral=True)
                    await m_i.followup.send(
                        f"Staff role {'added successfully' if success else 'already exists'}", 
                        ephemeral=True
                    )
                except ValueError:
                    await m_i.response.send_message("Invalid role ID", ephemeral=True)
                except Exception as e:
                    print(f"Error in set staff modal: {e}")
                    try:
                        await m_i.response.send_message("An error occurred while setting the staff role.", ephemeral=True)
                    except:
                        await m_i.followup.send("An error occurred while setting the staff role.", ephemeral=True)
            
            modal.on_submit = modal_callback
            await i.response.send_modal(modal)
            
        async def set_transcript_callback(i: discord.Interaction):
            modal = discord.ui.Modal(title="Set Transcript Channel")
            modal.add_item(discord.ui.TextInput(label="Channel ID", placeholder="Enter channel ID"))
            
            async def modal_callback(m_i: discord.Interaction):
                try:
                    channel_id = int(modal.children[0].value)
                    success = await set_transcript_channel(channel_id)
                    await m_i.response.send_message("Transcript channel set successfully!", ephemeral=True)
                except ValueError:
                    await m_i.response.send_message("Invalid channel ID", ephemeral=True)
            
            modal.on_submit = modal_callback
            await i.response.send_modal(modal)

        async def set_ticket_channel_callback(i: discord.Interaction):
            modal = discord.ui.Modal(title="Set Ticket Channel")
            modal.add_item(discord.ui.TextInput(label="Channel ID", placeholder="Enter channel ID"))
            
            async def modal_callback(m_i: discord.Interaction):
                try:
                    await m_i.response.defer(ephemeral=True)
                    channel_id = int(modal.children[0].value)
                    channel = m_i.guild.get_channel(channel_id)
                    if not channel:
                        await m_i.followup.send("Channel not found!", ephemeral=True)
                        return
                    
                    await set_ticket_channel(channel_id)
                    await create_ticket_panel(channel)
                    await m_i.followup.send("Ticket channel set and panel created successfully!", ephemeral=True)
                except ValueError:
                    await m_i.followup.send("Invalid channel ID", ephemeral=True)
                except Exception as e:
                    print(f"Error in set_ticket_channel modal: {e}")
                    await m_i.followup.send("An error occurred while setting up the ticket channel.", ephemeral=True)
            
            modal.on_submit = modal_callback
            await i.response.send_modal(modal)
        
        add_category_btn.callback = add_category_callback
        set_staff_btn.callback = set_staff_callback
        set_transcript_btn.callback = set_transcript_callback
        set_ticket_channel_btn.callback = set_ticket_channel_callback
        remove_category_btn.callback = remove_category_callback
        
        view.add_item(add_category_btn)
        view.add_item(set_staff_btn)
        view.add_item(set_transcript_btn)
        view.add_item(set_ticket_channel_btn)
        view.add_item(remove_category_btn)
        
        await interaction.followup.send(embed=embed, view=view)
        
    except Exception as e:
        logging.error(f"Error in setup_tickets_slash: {e}")
        await interaction.followup.send("An error occurred while setting up the ticket system.", ephemeral=True)

@tree.command(name="set_vanity", description="Set the vanity status and role")
@app_commands.default_permissions(administrator=True)
async def set_vanity(interaction: discord.Interaction, status: str, role: discord.Role):
    if not log_permission_check(interaction, "set_vanity"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Defer the response to prevent timeout
        await interaction.response.defer(ephemeral=True)
        
        # Get or create guild settings
        settings = get_guild_settings(interaction.guild_id)
        if not settings:
            settings = {
                "guild_id": interaction.guild_id,
                "vanity": {},
                "notification_channel_id": None
            }
        
        # Update vanity settings
        settings["vanity"].update({
            "url": status,
            "role_name": role.name
        })
        
        # Save settings
        save_guild_settings(interaction.guild_id, settings)
        
        # Send confirmation message
        await interaction.followup.send(
            f"✅ Vanity configuration updated:\n• Status: `{status}`\n• Role: {role.mention}\n\nNow, let's customize your notification messages!",
            ephemeral=True
        )
        
        # Show the notification customization modal
        modal = VanityNotificationModal()
        await interaction.response.send_modal(modal)
        
    except discord.Forbidden:
        await interaction.followup.send(
            "❌ I don't have the required permissions to perform this action.",
            ephemeral=True
        )
    except Exception as e:
        logging.error(f"Error in set_vanity: {str(e)}")
        await interaction.followup.send(
            "❌ An error occurred while setting up the vanity configuration. Please check the logs.",
            ephemeral=True
        )

@tree.command(name="edit_vanity_notification", description="Edit vanity notification messages")
@app_commands.default_permissions(administrator=True)
async def edit_vanity_notification(interaction: discord.Interaction):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    
    # Get current guild settings
    settings = get_guild_settings(interaction.guild_id)
    if not settings:
        settings = {
            "guild_id": interaction.guild_id,
            "vanity": {},
            "notification_channel_id": None
        }
    
    # Show the notification customization modal
    modal = VanityNotificationModal()
    await interaction.response.send_modal(modal)

@tree.command(name="set_notification_channel", description="Set the notification channel")
@app_commands.default_permissions(administrator=True)
async def set_notification_channel(interaction: discord.Interaction, channel: discord.TextChannel):
    if not log_permission_check(interaction, "set_notification_channel"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Defer the response to prevent timeout
        await interaction.response.defer(ephemeral=True)
        
        # Get current guild settings
        settings = get_guild_settings(interaction.guild_id)
        if not settings:
            settings = {
                "guild_id": interaction.guild_id,
                "vanity": {},
                "notification_channel_id": None
            }
        
        # Update notification channel
        settings["notification_channel_id"] = channel.id
        
        # Save to guild settings collection
        success = save_guild_settings(interaction.guild_id, settings)
        
        if not success:
            await interaction.followup.send(
                "❌ Failed to save notification channel settings. Please try again.",
                ephemeral=True
            )
            return
        
        await interaction.followup.send(
            f"✅ Notification channel has been set to {channel.mention}",
            ephemeral=True
        )
    except Exception as e:
        logging.error(f"Error in set_notification_channel: {str(e)}")
        await interaction.followup.send(
            "❌ An error occurred while setting the notification channel. Please check the logs.",
            ephemeral=True
        )

class VanityNotificationModal(Modal):
    def __init__(self):
        super().__init__(title="Customize Vanity Notifications")
        
        self.add_item(TextInput(
            label="Add Role Title",
            placeholder="Enter title for when role is added",
            default="Priority Queue Granted",
            required=True
        ))
        
        self.add_item(TextInput(
            label="Add Role Message",
            placeholder="Use {member} for user mention and {status} for vanity status",
            default="{member} just added '{status}' as their custom Discord status and received free priority queue!",
            style=discord.TextStyle.paragraph,
            required=True
        ))
        
        self.add_item(TextInput(
            label="Remove Role Title",
            placeholder="Enter title for when role is removed",
            default="Priority Queue Removed",
            required=True
        ))
        
        self.add_item(TextInput(
            label="Remove Role Message",
            placeholder="Use {member} for user mention and {status} for vanity status",
            default="{member} has removed '{status}' from their custom Discord status",
            style=discord.TextStyle.paragraph,
            required=True
        ))

    async def on_submit(self, interaction: discord.Interaction):
        try:
            settings = get_guild_settings(interaction.guild_id)
            if not settings:
                settings = {
                    "guild_id": interaction.guild_id,
                    "vanity": {},
                    "notification_channel_id": None
                }
            
            settings["vanity"].update({
                "add_role_title": self.children[0].value,
                "add_role_message": self.children[1].value,
                "remove_role_title": self.children[2].value,
                "remove_role_message": self.children[3].value
            })
            
            save_guild_settings(interaction.guild_id, settings)
            await interaction.response.send_message("✅ Vanity notification messages have been updated!", ephemeral=True)
        except Exception as e:
            logging.error(f"Error in VanityNotificationModal.on_submit: {str(e)}")
            await interaction.response.send_message("❌ An error occurred while saving notification settings.", ephemeral=True)

@tree.command(name="remove_vanity", description="Remove the vanity status and role")
@app_commands.default_permissions(administrator=True)
async def remove_vanity(interaction: discord.Interaction):
    if not log_permission_check(interaction, "remove_vanity"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Get current guild settings
        settings = get_guild_settings(interaction.guild_id)
        
        # Reset vanity settings while preserving notification messages
        if "vanity" in settings:
            notification_settings = {
                key: settings["vanity"][key]
                for key in ["add_role_title", "add_role_message", "remove_role_title", "remove_role_message"]
                if key in settings["vanity"]
            }
            settings["vanity"] = {
                "url": None,
                "role_name": None,
                **notification_settings
            }
            
        # Save updated settings
        save_guild_settings(interaction.guild_id, settings)
        
        # Send confirmation message
        await interaction.response.send_message("✅ Vanity status has been removed successfully.", ephemeral=True)
    except Exception as e:
        logging.error(f"Error in remove_vanity: {str(e)}")
        await interaction.response.send_message("❌ An error occurred while removing the vanity configuration.", ephemeral=True)

# In the embed message section
class EmbedModal(Modal):
    def __init__(self):
        super().__init__(title="Create Embed Message")
        
        self.add_item(TextInput(
            label="Title",
            placeholder="Enter the title for your embed",
            required=True
        ))
        
        self.add_item(TextInput(
            label="Description",
            placeholder="Enter the description for your embed",
            style=discord.TextStyle.paragraph,
            required=True
        ))
        
        self.add_item(TextInput(
            label="Color (Hex)",
            placeholder="#RRGGBB (e.g., #FF0000 for red) - Leave empty for default",
            required=False
        ))
        
        self.add_item(TextInput(
            label="Image URL",
            placeholder="Enter an image URL (optional)",
            required=False
        ))
        
        self.add_item(TextInput(
            label="Footer",
            placeholder="Enter footer text (optional)",
            required=False
        ))

    async def on_submit(self, interaction: discord.Interaction):
        self.interaction = interaction



@tree.command(name="send_embed", description="Send an embedded message to a channel")
@app_commands.default_permissions(administrator=True)
async def send_embed(interaction: discord.Interaction, channel: discord.TextChannel, mention: str = None):
    if not log_permission_check(interaction, "send_embed"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Send the modal
        modal = EmbedModal()
        await interaction.response.send_modal(modal)
        
        # Wait for modal submission
        await modal.wait()
        
        # Get values from modal
        title = modal.children[0].value
        description = modal.children[1].value
        color_hex = modal.children[2].value
        image_url = modal.children[3].value
        footer = modal.children[4].value
        
        # Create embed
        try:
            embed_color = int(color_hex.strip('#'), 16) if color_hex and color_hex.startswith('#') else discord.Color.blue().value
        except ValueError:
            embed_color = discord.Color.blue().value
        
        embed = discord.Embed(
            title=title,
            description=description,
            color=embed_color
        )
        
        # Set image if provided and valid
        if image_url and (image_url.startswith('http://') or image_url.startswith('https://')):
            embed.set_image(url=image_url)
            
        # Set footer if provided
        if footer:
            embed.set_footer(text=footer)
            
        # Handle mentions
        content = None
        if mention:
            mention = mention.strip().lower()
            if mention == "@everyone" or mention == "everyone":
                content = "@everyone"  # Handle both with and without @ prefix
            elif mention == "@here" or mention == "here":
                content = "@here"  # Handle both with and without @ prefix
            elif mention.startswith("<@") and mention.endswith(">"):
                content = mention
            else:
                # Try to find role by name
                role = discord.utils.get(interaction.guild.roles, name=mention)
                if role:
                    content = role.mention
                else:
                    # Try to find user by name
                    user = discord.utils.get(interaction.guild.members, name=mention)
                    if user:
                        content = user.mention
                    else:
                        await interaction.followup.send(f"Could not find user or role with name: {mention}", ephemeral=True)
                        return
        
        # Send the embed with allowed mentions
        await channel.send(
            content=content, 
            embed=embed, 
            allowed_mentions=discord.AllowedMentions(everyone=True, roles=True, users=True)
        )
        
        # Send confirmation
        await modal.interaction.response.send_message(f"Embed sent to {channel.mention} successfully!", ephemeral=True)
        
    except discord.Forbidden:
        await modal.interaction.response.send_message("I don't have permission to send messages in that channel.", ephemeral=True)
    except Exception as e:
        logging.error(f"Error in send_embed: {e}")
        await modal.interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)




async def delete_command_messages(interaction):
    """Delete messages related to the command, excluding the final confirmation message."""
    try:
        messages_to_delete = []
        async for message in interaction.channel.history(limit=100):
            # Check if the message is from the user who initiated the command or the bot
            if message.author == interaction.user or message.author == bot.user:
                # Check if the message is part of the command conversation
                if message.id == interaction.id or message.content.startswith("Let's create your embed!"):
                    messages_to_delete.append(message)

        # Delete messages with delay to avoid rate limits
        for message in messages_to_delete:
            try:
                await message.delete()
                await asyncio.sleep(0.5)  # Add 500ms delay between deletions
            except discord.NotFound:
                continue  # Message already deleted
            except discord.Forbidden:
                print(f"Missing permissions to delete message {message.id}")
                continue
            except Exception as e:
                print(f"Error deleting message {message.id}: {e}")
                continue

    except Exception as e:
        print(f"Error in delete_command_messages: {e}")

async def ask_for_input(interaction, prompt):
    """Helper function to ask for user input."""
    await interaction.channel.send(prompt)
    try:
        response = await bot.wait_for('message', timeout=60.0, check=lambda m: m.author == interaction.user)
        return response.content
    except asyncio.TimeoutError:
        await interaction.channel.send("You took too long to respond!")
        return None

async def ask_for_channel(interaction, prompt):
    """Helper function to ask for a channel mention."""
    await interaction.channel.send(prompt)
    try:
        response = await bot.wait_for('message', timeout=60.0, check=lambda m: m.author == interaction.user)
        channel_id = int(response.content.strip('<>#'))
        return interaction.guild.get_channel(channel_id)
    except (ValueError, TypeError):
        await interaction.channel.send("Invalid channel mention. Please try again.")
        return None
    except asyncio.TimeoutError:
        await interaction.channel.send("You took too long to respond!")
        return None

# Auto Role System
@tree.command(name="set_join_role", description="Set the join role")
@app_commands.default_permissions(administrator=True)
async def set_join_role(interaction: discord.Interaction, role: discord.Role):
    if not log_permission_check(interaction, "set_join_role"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    global join_role_id
    join_role_id = role.id
    await save_data()  # Save data after setting the welcome channel
    await interaction.response.send_message(f"Join role set to '{role.name}'.")

@bot.event
async def on_member_join(member):
    if join_role_id:
        role = member.guild.get_role(join_role_id)
        if role:
            await member.add_roles(role)
            print(f"Assigned join role '{role.name}' to {member.name}.")
 
# Gang Management System
@tree.command(name="create_gang", description="Create a new gang")
@app_commands.default_permissions(administrator=True)
async def create_gang(interaction: discord.Interaction, gang_name: str, leader: discord.Member, leader_role: discord.Role, member_limit: int):
    if gang_name in gang_roles:
        await interaction.response.send_message(f"Gang '{gang_name}' already exists.")
        return
    try:
        # Debug prints before creating gang
        print("\nCreating gang with details:")
        print(f"Leader ID: {leader.id}")
        print(f"Leader ID (str): {str(leader.id)}")
        print(f"Current gang_roles: {gang_roles}")
        print(f"Current gang_leaders: {gang_leaders}")

        # Assign the leader role
        await leader.add_roles(leader_role)
        print(f"Assigned role '{leader_role.name}' to {leader.name}")

        # Store gang data
        gang_roles[gang_name] = {
            "leader": leader.id,  # Store as integer
            "leader_role": leader_role.id,
            "members": [],
            "member_limit": member_limit,
            "current_members": 0
        }

        # Store leader data
        gang_leaders[leader.id] = leader_role.id  # Store as integer, not string

        # Debug prints after storing data
        print("\nAfter creating gang:")
        print(f"Updated gang_roles: {gang_roles}")
        print(f"Updated gang_leaders: {gang_leaders}")

        await save_data()
        await interaction.response.send_message(
            f"Gang '{gang_name}' created successfully!\n"
            f"Leader: {leader.mention}\n"
            f"Leader Role: {leader_role.mention}\n"
            f"Member Limit: {member_limit}"
        )

    except discord.Forbidden:
        await interaction.response.send_message("I don't have permission to assign roles.")
    except Exception as e:
        await interaction.response.send_message(f"Error creating gang: {str(e)}")

@tree.command(name="assign_member_role", description="Assign a member role to a gang leader role")
@app_commands.default_permissions(administrator=True)
async def assign_member_role(interaction: discord.Interaction, leader_role: discord.Role, member_role: discord.Role):
    # Check if the leader role is valid
    if leader_role.id not in gang_leaders.values():
        await interaction.response.send_message(f"The role '{leader_role.name}' is not a leader role.")
        return

    # Ensure the gang_roles dictionary has an entry for the leader role
    gang_name = next((name for name, details in gang_roles.items() if details["leader_role"] == leader_role.id), None)
    if gang_name is None:
        await interaction.response.send_message(f"No gang found for the leader role '{leader_role.name}'.")
        return

    # Check if the member role is already associated
    if member_role.id in gang_roles[gang_name]["members"]:
        await interaction.response.send_message(f"The role '{member_role.name}' is already associated with the leader role '{leader_role.name}'.")
        return

    # Add the member role to the gang's associated roles
    gang_roles[gang_name]["members"].append(member_role.id)
    await save_data()  # Save the updated gang roles
    await interaction.response.send_message(f"The role '{member_role.name}' has been associated with the leader role '{leader_role.name}'.")

@tree.command(name="manage_gang", description="Manage gang members")
async def manage_gang(interaction: discord.Interaction, member: discord.Member, action: str):
    """
    Manage gang members by adding or removing them.
    
    Parameters:
    -----------
    member: The member to add/remove
    action: Either 'add' or 'remove'
    """
    try:
        await interaction.response.defer()
        
        # Find gang where user is leader
        gang_name = None
        user_id = str(interaction.user.id)  # Convert user ID to string for comparison
        for name, details in gang_roles.items():
            # Compare string IDs since MongoDB stores them as strings
            if str(details["leader"]) == user_id:
                gang_name = name
                break

        if gang_name is None:
            await interaction.followup.send("You are not a gang leader.", ephemeral=True)
            return

        if action == "add":
            # Check member limit before adding
            if gang_roles[gang_name]["current_members"] >= gang_roles[gang_name]["member_limit"]:
                await interaction.followup.send(f"Cannot add more members. Gang has reached its limit of {gang_roles[gang_name]['member_limit']} members.")
                return

            if member.id not in gang_roles[gang_name]["members"]:
                # Get all member roles associated with the gang
                member_role_ids = [role_id for role_id in gang_roles[gang_name]["members"] if isinstance(role_id, int)]
                
                if not member_role_ids:
                    await interaction.followup.send("No member roles are associated with this gang. Please add a member role first.")
                    return
                
                # Try to find a valid member role
                member_role = None
                for role_id in member_role_ids:
                    role = interaction.guild.get_role(role_id)
                    if role:
                        member_role = role
                        break
                
                if member_role:
                    try:
                        await member.add_roles(member_role)
                        gang_roles[gang_name]["members"].append(member.id)
                        gang_roles[gang_name]["current_members"] += 1
                        await save_data()
                        await interaction.followup.send(
                            f"Added {member.mention} to gang '{gang_name}' with role '{member_role.name}'.\n"
                            f"Current members: {gang_roles[gang_name]['current_members']}/{gang_roles[gang_name]['member_limit']}"
                        )
                    except discord.Forbidden:
                        await interaction.followup.send("I don't have permission to assign roles.")
                    except Exception as e:
                        await interaction.followup.send(f"Error adding member: {str(e)}")
                else:
                    await interaction.followup.send("Could not find a valid member role. Please contact an administrator.")
            else:
                await interaction.followup.send(f"{member.mention} is already a member of gang '{gang_name}'.")

        elif action == "remove":
            if member.id in gang_roles[gang_name]["members"]:
                # Get member role IDs
                member_role_ids = [role_id for role_id in gang_roles[gang_name]["members"] if isinstance(role_id, int)]
                
                # Remove member roles
                for role_id in member_role_ids:
                    role = interaction.guild.get_role(role_id)
                    if role and role in member.roles:
                        await member.remove_roles(role)
                
                gang_roles[gang_name]["members"].remove(member.id)
                gang_roles[gang_name]["current_members"] -= 1
                await save_data()
                await interaction.followup.send(
                    f"Removed {member.mention} from gang '{gang_name}'.\n"
                    f"Current members: {gang_roles[gang_name]['current_members']}/{gang_roles[gang_name]['member_limit']}"
                )
            else:
                await interaction.followup.send(f"{member.mention} is not a member of gang '{gang_name}'.")

        else:
            await interaction.followup.send("Invalid action. Use 'add' or 'remove'.")

    except discord.Forbidden:
        logging.error(f"Permission error in manage_gang: {interaction.guild.id}")
        await interaction.followup.send("I don't have permission to manage roles.", ephemeral=True)
    except discord.HTTPException as e:
        logging.error(f"Discord API error in manage_gang: {e}")
        await interaction.followup.send(f"Discord API error: {str(e)}", ephemeral=True)
    except Exception as e:
        logging.error(f"Error in manage_gang: {e}")
        await interaction.followup.send("An error occurred while managing the gang.", ephemeral=True)

@tree.command(name="remove_gang", description="Remove a gang")
@app_commands.default_permissions(administrator=True)
async def remove_gang(interaction: discord.Interaction, name: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    if name in gang_roles:
        del gang_roles[name]
        # Also remove from gang_leaders if they exist
        gang_leader_id = gang_leaders.pop(next((id for id, role in gang_leaders.items() if role == name), None), None)
        if gang_leader_id:
            del gang_leaders[gang_leader_id]
        await save_data()  # Save the updated gang roles
        await interaction.response.send_message(f"Gang '{name}' removed.")
    else:
        await interaction.response.send_message(f"Gang '{name}' not found.")

@tree.command(name="check_gang_list", description="Check the list of gangs and their members")
@app_commands.default_permissions(administrator=True)
async def check_gang_list(interaction: discord.Interaction):
    if not gang_roles:
        await interaction.response.send_message("No gangs found.")
        return

    embed = discord.Embed(title="Gang List", color=discord.Color.blue())
    for gang_name, details in gang_roles.items():
        leader = interaction.guild.get_member(details["leader"])
        members = [interaction.guild.get_member(member_id) for member_id in details["members"]]
        members_list = ", ".join(member.mention for member in members if member) or "No members"
        
        strike_count = gang_strikes.get(gang_name, 0)
        strike_text = f"⚠️ Strikes: {strike_count}" if strike_count > 0 else "✅ No strikes"
        
        embed.add_field(
            name=gang_name, 
            value=(
                f"Leader: {leader.mention if leader else 'Unknown'}\n"
                f"Members ({details['current_members']}/{details['member_limit']}): {members_list}\n"
                f"{strike_text}"
            ), 
            inline=False
        )

    await interaction.response.send_message(embed=embed)

# Add this command in the Gang Management System section
@tree.command(name="remove_strike", description="Remove a strike from a gang")
@app_commands.default_permissions(administrator=True)
async def remove_strike(interaction: discord.Interaction, gang_name: str, reason: str = None):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    if gang_name not in gang_roles:
        await interaction.response.send_message(f"Gang '{gang_name}' not found.")
        return

    if gang_name not in gang_strikes or gang_strikes[gang_name] <= 0:
        await interaction.response.send_message(f"Gang '{gang_name}' has no strikes to remove.")
        return

    gang_strikes[gang_name] -= 1
    await save_data()

    # Notify gang members
    leader_id = gang_roles[gang_name]["leader"]
    members = gang_roles[gang_name]["members"]

    # Create notification message
    notification = f"A strike has been removed from your gang '{gang_name}'. Current strikes: {gang_strikes[gang_name]}\nReason: {reason}"
    if reason:
        notification += f"\nReason: {reason}"

    # Notify leader
    leader = interaction.guild.get_member(leader_id)
    if leader:
        await leader.send(notification)

    # Notify members
    for member_id in members:
        member = interaction.guild.get_member(member_id)
        if member:
            try:
                await member.send(notification)
            except:
                logging.warning(f"Could not notify member {member.id} about gang changes")

    await interaction.response.send_message(
        f"Strike removed from gang '{gang_name}'. Current strikes: {gang_strikes[gang_name]}"
    )

# Add this command for gang leaders to view their gang stats
@tree.command(name="view_my_gang", description="View your gang's statistics")
async def view_my_gang(interaction: discord.Interaction):
    try:
        await interaction.response.defer()
        
        # Find gang where user is leader
        user_id = interaction.user.id
        gang_name = None
        
        for name, details in gang_roles.items():
            if details["leader"] == user_id:
                gang_name = name
                break

        if gang_name is None:
            await interaction.followup.send("You are not a gang leader.", ephemeral=True)
            return

        gang_details = gang_roles[gang_name]
        # Get member list excluding role IDs
        member_ids = [mid for mid in gang_details["members"] if isinstance(mid, int)]
        members = [interaction.guild.get_member(member_id) for member_id in member_ids]
        members_list = ", ".join(member.mention for member in members if member) or "No members"

        embed = discord.Embed(
            title=f"Gang Statistics: {gang_name}",
            color=discord.Color.blue()
        )

        embed.add_field(
            name="Members",
            value=f"Current: {gang_details['current_members']}/{gang_details['member_limit']}\n{members_list}",
            inline=False
        )

        embed.add_field(
            name="Strikes",
            value=f"Current Strikes: {gang_strikes.get(gang_name, 0)}",
            inline=False
        )

        await interaction.followup.send(embed=embed)
        
    except Exception as e:
        print(f"Error in view_my_gang: {e}")
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message("An error occurred while processing your request.", ephemeral=True)
        except:
            pass

# Add these helper functions in the Gang Management System section, before the commands

async def notify_gang_changes(gang_name, changes, interaction):
    """Notify gang members about changes"""
    for member_id in gang_roles[gang_name]["members"]:
        if isinstance(member_id, int):
            member = interaction.guild.get_member(member_id)
            if member:
                try:
                    await member.send(f"Your gang '{gang_name}' has been updated:\n" + "\n".join(changes))
                except:
                    logging.warning(f"Could not notify member {member.id} about gang changes")

def verify_member_count(gang_name):
    """Verify and correct member count"""
    actual_members = len([m for m in gang_roles[gang_name]["members"] if isinstance(m, int)])
    gang_roles[gang_name]["current_members"] = actual_members
    return actual_members

def check_bot_permissions(guild):
    """Check if bot has required permissions"""
    bot_member = guild.get_member(bot.user.id)
    required_permissions = ["manage_roles", "send_messages", "embed_links"]
    return [perm for perm in required_permissions if not getattr(bot_member.guild_permissions, perm)]

@tree.command(name="issue_strike", description="Issue a strike to a gang")
@app_commands.default_permissions(administrator=True)
async def issue_strike(interaction: discord.Interaction, gang_name: str, reason: str):
    try:
        # Defer the response immediately to prevent timeout
        await interaction.response.defer()

        if gang_name not in gang_roles:
            await interaction.followup.send(f"Gang '{gang_name}' not found.")
            return

        # Initialize strikes for the gang if not exists
        if gang_name not in gang_strikes:
            gang_strikes[gang_name] = 0

        # Add the strike
        gang_strikes[gang_name] += 1
        await save_data()

        # Notify gang members
        leader_id = gang_roles[gang_name]["leader"]
        members = gang_roles[gang_name]["members"]

        # Create notification message
        notification = f"Your gang '{gang_name}' has received a strike. Current strikes: {gang_strikes[gang_name]}\nReason: {reason}"

        # Notify leader
        leader = interaction.guild.get_member(leader_id)
        if leader:
            try:
                await leader.send(notification)
            except:
                print(f"Could not DM leader {leader.name}")

        # Notify members
        for member_id in members:
            member = interaction.guild.get_member(member_id)
            if member:
                try:
                    await member.send(notification)
                except:
                    print(f"Could not DM member {member.name}")

        await interaction.followup.send(
            f"Strike issued to gang '{gang_name}'. Current strikes: {gang_strikes[gang_name]}\nReason: {reason}"
        )

    except Exception as e:
        print(f"Error in issue_strike: {e}")
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message("An error occurred while processing your request.", ephemeral=True)
        except:
            pass

# Updated edit_gang command
@tree.command(name="edit_gang", description="Edit gang settings")
@app_commands.default_permissions(administrator=True)
async def edit_gang(interaction: discord.Interaction, gang_name: str, new_member_limit: int = None, new_leader: discord.Member = None):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        await interaction.response.defer()
        
        # Initial validations
        if gang_name not in gang_roles:
            await interaction.followup.send("Gang not found.", ephemeral=True)
            return
            
        # Check bot permissions
        missing_permissions = check_bot_permissions(interaction.guild)
        if missing_permissions:
            await interaction.followup.send(
                f"Bot is missing required permissions: {', '.join(missing_permissions)}", 
                ephemeral=True
            )
            return

        # Create backup before making changes
        await create_backup()
        
        # Store original data for rollback
        original_data = {
            "gang_roles": gang_roles.copy(),
            "gang_leaders": gang_leaders.copy()
        }
        
        try:
            changes = []
            if new_member_limit is not None:
                if new_member_limit <= 0:
                    await interaction.followup.send("Member limit must be greater than 0", ephemeral=True)
                    return
                
                if new_member_limit < gang_roles[gang_name]["current_members"]:
                    await interaction.followup.send(
                        "New member limit cannot be less than current member count.", 
                        ephemeral=True
                    )
                    return
                    
                old_limit = gang_roles[gang_name]["member_limit"]
                gang_roles[gang_name]["member_limit"] = new_member_limit
                changes.append(f"Member limit changed from {old_limit} to {new_member_limit}")
                
            if new_leader:
                # Check if new leader is already leading another gang
                for other_gang, details in gang_roles.items():
                    if details["leader"] == new_leader.id and other_gang != gang_name:
                        await interaction.followup.send(
                            "This user is already a leader of another gang.", 
                            ephemeral=True
                        )
                        return

                old_leader_id = gang_roles[gang_name]["leader"]
                old_leader = interaction.guild.get_member(old_leader_id)
                
                # Remove new leader from members list if they're there
                if new_leader.id in gang_roles[gang_name]["members"]:
                    gang_roles[gang_name]["members"].remove(new_leader.id)
                    gang_roles[gang_name]["current_members"] -= 1
                
                # Update gang data
                gang_roles[gang_name]["leader"] = new_leader.id
                gang_leaders[new_leader.id] = gang_roles[gang_name]["leader_role"]
                
                # Remove old leader's data
                if old_leader_id in gang_leaders:
                    del gang_leaders[old_leader_id]
                
                # Update leader roles
                leader_role_id = gang_roles[gang_name]["leader_role"]
                leader_role = interaction.guild.get_role(leader_role_id)
                
                if leader_role:
                    # Handle old leader
                    if old_leader:
                        await old_leader.remove_roles(leader_role)
                        # Convert old leader to member if there's a member role
                        member_role_id = next((role_id for role_id in gang_roles[gang_name]["members"] 
                                             if isinstance(role_id, int)), None)
                        if member_role_id:
                            member_role = interaction.guild.get_role(member_role_id)
                            if member_role:
                                await old_leader.add_roles(member_role)
                                gang_roles[gang_name]["members"].append(old_leader_id)
                                gang_roles[gang_name]["current_members"] += 1
                    
                    # Assign new leader role
                    await new_leader.add_roles(leader_role)
                    
                changes.append(f"Leader changed from {old_leader.mention if old_leader else 'Unknown'} to {new_leader.mention}")
            
            # Verify member count
            verify_member_count(gang_name)
            
            await save_data()
            
            # Create response embed
            embed = discord.Embed(
                title="Gang Settings Updated",
                description=f"Gang: {gang_name}",
                color=discord.Color.green()
            )
            
            for change in changes:
                embed.add_field(name="Change", value=change, inline=False)
                
            # Notify members about changes
            await notify_gang_changes(gang_name, changes, interaction)
                
            await interaction.followup.send(embed=embed)
            logging.info(f"Gang {gang_name} edited by {interaction.user}: {', '.join(changes)}")
            
        except Exception as e:
            # Rollback changes on error
            gang_roles.update(original_data["gang_roles"])
            gang_leaders.update(original_data["gang_leaders"])
            raise e
            
    except discord.Forbidden:
        logging.error(f"Permission error in edit_gang: {interaction.guild.id}")
        await interaction.followup.send("I don't have permission to manage roles.", ephemeral=True)
    except discord.HTTPException as e:
        logging.error(f"Discord API error in edit_gang: {e}")
        await interaction.followup.send(f"Discord API error: {str(e)}", ephemeral=True)
    except Exception as e:
        logging.error(f"Error editing gang: {e}")
        await interaction.followup.send("An error occurred while editing the gang.", ephemeral=True)

# Application System
@tree.command(name="set_application_channel", description="Set the application channel")
@app_commands.default_permissions(administrator=True)
async def set_application_channel(interaction: discord.Interaction, channel: discord.TextChannel):
    global application_channel_id
    application_channel_id = channel.id
    await save_data()  # Save data after setting the welcome channel
    await interaction.response.send_message(f"Application channel set to {channel.mention}")
    await send_application_embed(channel)

@tree.command(name="set_application_log_channel", description="Set the application log channel")
@app_commands.default_permissions(administrator=True)
async def set_application_log_channel(interaction: discord.Interaction, channel: discord.TextChannel):
    global application_log_channel
    application_log_channel = channel.id
    await save_data()  # Save data after setting the welcome channel
    await interaction.response.send_message(f"Application log channel set to {channel.mention}")

@tree.command(name="create_application", description="Create a new application form")
@app_commands.default_permissions(administrator=True)
async def create_application(interaction: discord.Interaction):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    class ApplicationModal(Modal):
        def __init__(self):
            super().__init__(title="Create Application")

            self.add_item(TextInput(label="Application Name", placeholder="Enter the application name"))
            self.add_item(TextInput(label="Questions", placeholder="Enter questions separated by semicolons", style=discord.TextStyle.long))

        async def on_submit(self, interaction: discord.Interaction):
            name = self.children[0].value
            questions = self.children[1].value.split(';')
            application_forms[name] = [q.strip() for q in questions if q.strip()]
            await save_data()
            await interaction.response.send_message(f"Application '{name}' created with {len(application_forms[name])} questions.")

    await interaction.response.send_modal(ApplicationModal())

@tree.command(name="list_applications", description="List all applications with questions")
@app_commands.default_permissions(administrator=True)
async def list_applications(interaction: discord.Interaction):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    if not application_forms:
        await interaction.response.send_message("No applications found.")
        return

    embed = discord.Embed(title="Application List", color=discord.Color.blue())
    for name, questions in application_forms.items():
        embed.add_field(name=name, value="\n".join(questions), inline=False)

    await interaction.response.send_message(embed=embed)

@tree.command(name="edit_application", description="Edit an existing application form")
@app_commands.default_permissions(administrator=True)
async def edit_application(interaction: discord.Interaction, name: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    log_permission_check(interaction, "edit_application")
    if name not in application_forms:
        await interaction.response.send_message(f"Application '{name}' not found.")
        return

    modal = EditApplicationModal(name)
    await interaction.response.send_modal(modal)

class EditApplicationModal(discord.ui.Modal):
    def __init__(self, name):
        super().__init__(title="Edit Application")
        self.old_name = name
        self.add_item(discord.ui.TextInput(label="Application Name", default=name))
        self.add_item(discord.ui.TextInput(label="Questions", default="; ".join(application_forms[name]), style=discord.TextStyle.long))

    async def on_submit(self, interaction: discord.Interaction):
        new_name = self.children[0].value
        questions = self.children[1].value.split(';')
        if new_name != self.old_name:
            application_forms[new_name] = application_forms.pop(self.old_name)
        application_forms[new_name] = [q.strip() for q in questions if q.strip()]
        await save_data()
        await interaction.response.send_message(f"Application '{new_name}' updated with {len(application_forms[new_name])} questions.")


@tree.command(name="remove_application", description="Remove an existing application form")
@app_commands.default_permissions(administrator=True)
async def remove_application(interaction: discord.Interaction, name: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    if name in application_forms:
        del application_forms[name]
        await save_data()  # Save data after setting the welcome channel
        await interaction.response.send_message(f"Application '{name}' has been removed.")
    else:
        await interaction.response.send_message(f"Application '{name}' does not exist.")

async def send_application_embed(channel):
    # Create options with emojis for each application form
    options = [
        discord.SelectOption(label=f"📝 {name}", value=name) for name in application_forms.keys()
    ]
    
    # Create a dropdown with a creative placeholder
    select = Select(placeholder="🌟 Select Your Application Form 🌟", options=options)

    async def select_callback(interaction):
        selected_application = select.values[0]
        embed = discord.Embed(
            title="🎉 Application Selected!",
            description=f"You've chosen the **{selected_application}** application! 🚀\n\nPlease confirm your choice below. You have **60 minutes** to complete it, or you'll need to restart the process.",
            color=discord.Color.green()  # A vibrant green for positivity
        )
        embed.set_thumbnail(url=interaction.guild.icon.url)  # Optional: Set a thumbnail (guild icon)
        embed.set_footer(text="Good luck with your application! 🍀", icon_url=interaction.user.avatar.url)  # Optional: Set a footer

        # Confirm buttons
        view = View()
        confirm_button = Button(label="✅ I want to apply!", style=discord.ButtonStyle.green)
        cancel_button = Button(label="❌ Cancel Application", style=discord.ButtonStyle.red)

        async def confirm_callback(interaction):
            await interaction.response.send_message("Application process started! Let's get started.")
            await start_application(interaction.user, selected_application)

        async def cancel_callback(interaction):
            await interaction.response.send_message("Application process canceled. If you need anything else, just let me know!")

        confirm_button.callback = confirm_callback
        cancel_button.callback = cancel_callback
        view.add_item(confirm_button)
        view.add_item(cancel_button)

        await interaction.user.send(embed=embed, view=view)

    select.callback = select_callback
    
    # Create an initial embed for the dropdown menu
    embed = discord.Embed(
        title="✨ Welcome to the Application Portal!",
        description="Please select one of the applications below to proceed. Your journey starts here! 🚀",
        color=discord.Color.blue()  # A calming blue for professionalism
    )
    embed.set_footer(text="Choose wisely! 🧐")
    
    # Send the embed with the dropdown menu
    await channel.send(embed=embed, view=View().add_item(select))

async def start_application(user, application_name):
    """
    Handles the application process for the user.
    """
    try:
        # Ensure application_forms is accessible
        if not application_forms:
            await user.send("No application forms are available. Please contact an admin.")
            return

        # Get the questions for the selected application
        questions = application_forms.get(application_name)
        if not questions:
            await user.send(f"Application '{application_name}' not found. Please contact an admin.")
            return

        answers = []
        start_time = datetime.now(timezone.utc)

        # Send initial application start message
        embed = discord.Embed(
            title="Application Process",
            description=f"Application '{application_name}' started. You have 60 minutes to complete it.",
            color=discord.Color.blue()
        )
        view = View()
        cancel_button = Button(label="Cancel Application", style=discord.ButtonStyle.red)

        async def cancel_callback(interaction):
            await interaction.response.send_message("Application canceled.")
            return

        cancel_button.callback = cancel_callback
        view.add_item(cancel_button)
        await user.send(embed=embed, view=view)

        # Ask questions one by one
        for i, question in enumerate(questions):
            await user.send(f"Question {i+1}/{len(questions)}: {question}")
            try:
                answer = await bot.wait_for('message', timeout=60.0, check=lambda m: m.author == user)
                answers.append(answer.content)
            except asyncio.TimeoutError:
                await user.send("You took too long to answer. The application has been canceled.")
                return

        # Send completion message to the user
        await user.send("Thank you for completing the application! Your responses have been recorded.")

        # Log the application
        await log_application(user, application_name, questions, answers, application_log_channel)

    except Exception as e:
        print(f"Error in start_application: {e}")
        await user.send("An error occurred while processing your application. Please try again later.")

        # Log the application
async def log_application(user, application_name, questions, answers, application_log_channel):
    """
    Logs the application details in the specified log channel.
    """
    try:
        if not application_log_channel:
            print("Application log channel not set.")
            return

        log_channel = bot.get_channel(application_log_channel)
        if not log_channel:
            print(f"Log channel with ID {application_log_channel} not found.")
            return

        start_time = datetime.now(timezone.utc)

        # Create the embed for the application log
        embed = discord.Embed(
            title=f"New Application: {application_name}",
            description=f"Applicant: {user.mention}\nTime: {start_time.strftime('%Y-%m-%d %H:%M:%S')}",
            color=discord.Color.blue()
        )
        for i, question in enumerate(questions):
            embed.add_field(name=f"Question {i+1}", value=question, inline=False)
            embed.add_field(name=f"Answer {i+1}", value=answers[i], inline=False)

        # Create buttons for response
        view = View()
        accept_button = Button(label="✅ Accept", style=discord.ButtonStyle.green)
        reject_button = Button(label="❌ Reject", style=discord.ButtonStyle.red)
        accept_reason_button = Button(label="📝 Accept with Reason", style=discord.ButtonStyle.blurple)
        reject_reason_button = Button(label="📝 Reject with Reason", style=discord.ButtonStyle.gray)

        async def accept_callback(interaction):
            if applications_status.get(user.id, {}).get("responded"):
                await interaction.response.send_message("This application has already been responded to.", ephemeral=True)
                return

            applications_status[user.id] = {"responded": True, "admin": interaction.user.id, "status": "accepted"}
            await save_data()
            await interaction.response.send_message("Application accepted!", ephemeral=True)
            await user.send(f"Your application for '{application_name}' has been accepted!")

        async def reject_callback(interaction):
            if applications_status.get(user.id, {}).get("responded"):
                await interaction.response.send_message("This application has already been responded to.", ephemeral=True)
                return

            applications_status[user.id] = {"responded": True, "admin": interaction.user.id, "status": "rejected"}
            await save_data()
            await interaction.response.send_message("Application rejected!", ephemeral=True)
            await user.send(f"Your application for '{application_name}' has been rejected.")

        async def accept_with_reason_callback(interaction):
            if applications_status.get(user.id, {}).get("responded"):
                await interaction.response.send_message("This application has already been responded to.", ephemeral=True)
                return
            await interaction.response.send_modal(AcceptReasonModal(user, application_name))

        async def reject_with_reason_callback(interaction):
            if applications_status.get(user.id, {}).get("responded"):
                await interaction.response.send_message("This application has already been responded to.", ephemeral=True)
                return
            await interaction.response.send_modal(RejectReasonModal(user, application_name))

        accept_button.callback = accept_callback
        reject_button.callback = reject_callback
        accept_reason_button.callback = accept_with_reason_callback
        reject_reason_button.callback = reject_with_reason_callback

        view.add_item(accept_button)
        view.add_item(reject_button)
        view.add_item(accept_reason_button)
        view.add_item(reject_reason_button)

        # Send the embed with buttons to the log channel
        log_message = await log_channel.send(embed=embed, view=view)
        
        # Store the message ID with the application status
        applications_status[user.id] = {
            "responded": False,
            "message_id": log_message.id,
            "application_name": application_name
        }
        await save_data()

    except Exception as e:
        print(f"Error in log_application: {e}")
        await user.send("An error occurred while logging your application.")

class AcceptReasonModal(discord.ui.Modal):
    def __init__(self, user, application_name):
        super().__init__(title="Accept Application with Reason")
        self.user = user
        self.application_name = application_name
        self.add_item(discord.ui.TextInput(label="Reason for Acceptance", style=discord.TextStyle.paragraph))

    async def on_submit(self, interaction):
        reason = self.children[0].value
        applications_status[self.user.id] = {"responded": True, "admin": interaction.user.id, "status": "accepted"}
        await save_data()
        await interaction.response.send_message("Application accepted with reason!", ephemeral=True)
        await self.user.send(f"Your application for '{self.application_name}' has been accepted!\nReason: {reason}")

class RejectReasonModal(discord.ui.Modal):
    def __init__(self, user, application_name):
        super().__init__(title="Reject Application with Reason")
        self.user = user
        self.application_name = application_name
        self.add_item(discord.ui.TextInput(label="Reason for Rejection", style=discord.TextStyle.paragraph))

    async def on_submit(self, interaction):
        reason = self.children[0].value
        applications_status[self.user.id] = {"responded": True, "admin": interaction.user.id, "status": "rejected"}
        await save_data()
        await interaction.response.send_message("Application rejected with reason!", ephemeral=True)
        await self.user.send(f"Your application for '{self.application_name}' has been rejected.\nReason: {reason}")
# Welcome Message System
@tree.command(name="set_welcome_channel", description="Set the welcome channel, message, and image/GIF")
@app_commands.default_permissions(administrator=True)
async def set_welcome_channel(interaction: discord.Interaction, channel: discord.TextChannel, message: str, image_url: str = None):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    global welcome_channel_id, welcome_message, welcome_image_url
    welcome_channel_id = channel.id
    welcome_message = message
    welcome_image_url = image_url
    await save_data()  # Save data after setting the welcome channel
    await interaction.response.send_message(f"Welcome channel set to {channel.mention} with message: {message}")

@bot.event
async def on_member_join(member):
    if welcome_channel_id:
        channel = bot.get_channel(welcome_channel_id)
        if channel:
            embed = discord.Embed(
                title="Welcome!",
                description=welcome_message,
                color=discord.Color.blue()
            )
            if welcome_image_url:
                embed.set_image(url=welcome_image_url)
            await channel.send(embed=embed)

@bot.event
async def on_interaction(interaction: discord.Interaction):
    if interaction.type == discord.InteractionType.component:
        custom_id = interaction.data.get("custom_id", "")
        
        if custom_id == "create_ticket":
            # Create category selection buttons
            view = View()
            
            # Get available categories
            categories = ticket_config.get("categories", {})
            if not categories:
                await interaction.response.send_message("No ticket categories are configured!", ephemeral=True)
                return
                
            # Create a button for each category
            for category_id, category_info in categories.items():
                button = Button(
                    label=category_info["name"],
                    custom_id=f"ticket_category_{category_id}",
                    style=discord.ButtonStyle.primary
                )
                view.add_item(button)
            
            # Send category selection message
            await interaction.response.send_message(
                "Please select a ticket category:",
                view=view,
                ephemeral=True
            )
            return
            
        elif custom_id.startswith("ticket_category_"):
            try:
                # Extract category_id and keep it as string
                category_id = custom_id.split("_")[-1]
                
                # Debug prints
                print(f"Received ticket category interaction for category: {category_id}")
                print(f"Available categories: {ticket_config.get('categories', {})}")
                
                # Verify category exists using string comparison
                if category_id not in ticket_config.get("categories", {}):
                    await interaction.response.send_message("Invalid ticket category! Please contact an administrator.", ephemeral=True)
                    return
                
                # Convert to int only when passing to create_ticket
                await create_ticket(interaction, int(category_id))
                return
            except ValueError as e:
                print(f"Error parsing category ID: {e}")
                await interaction.response.send_message("Invalid ticket category format.", ephemeral=True)
                return
            except Exception as e:
                print(f"Error creating ticket: {e}")
                await interaction.response.send_message("An error occurred while creating the ticket.", ephemeral=True)
                return
            
        elif custom_id == "close_ticket":
            success, error = await close_ticket(interaction.channel_id)
            if not success:
                await interaction.response.send_message(f"Error closing ticket: {error}", ephemeral=True)
                
        elif custom_id == "close_with_reason":
            modal = discord.ui.Modal(title="Close Ticket")
            modal.add_item(discord.ui.TextInput(label="Reason", style=discord.TextStyle.paragraph))
            
            async def modal_callback(m_i: discord.Interaction):
                reason = modal.children[0].value
                success, error = await close_ticket(m_i.channel_id, reason)
                if not success:
                    await m_i.response.send_message(f"Error closing ticket: {error}", ephemeral=True)
            
            modal.on_submit = modal_callback
            await interaction.response.send_modal(modal)
            
        elif custom_id == "claim_ticket":
            # Check if user has staff role
            has_staff_role = False
            for role_id in ticket_config["staff_roles"]:
                role = interaction.guild.get_role(role_id)
                if role and role in interaction.user.roles:
                    has_staff_role = True
                    break
            
            if not has_staff_role:
                await interaction.response.send_message("You don't have permission to claim tickets.", ephemeral=True)
                return
                
            success, error = await claim_ticket(interaction.channel_id, interaction.user)
            if not success:
                await interaction.response.send_message(f"Error claiming ticket: {error}", ephemeral=True)
            else:
                await interaction.response.send_message("Ticket claimed successfully!", ephemeral=True)





async def restore_ticket_panel():
    """Restore ticket panel with rate limit handling"""
    try:
        if not ticket_config["ticket_channel"]:
            return
            
        channel = bot.get_channel(ticket_config["ticket_channel"])
        if not channel:
            print("Ticket channel not found")
            return
            
        print("Starting ticket panel restoration...")
        
        # Clear existing messages with rate limit handling
        async for message in channel.history(limit=100):
            if message.author == bot.bot:
                await rate_limiter.execute(
                    'delete_message',
                    message.delete
                )
        
        # Create new panel
        await create_ticket_panel(channel)
        print("Ticket panel restored successfully")
        
    except Exception as e:
        print(f"Error restoring ticket panel: {e}")


@bot.event
async def on_message(message):
    try:
        # Ignore messages from the bot itself
        if message.author == bot.user:
            return

        # Process commands first
        await bot.process_commands(message)

        # Handle sticky messages with optimized performance
        if message.channel.id in sticky_messages:
            sticky_message = sticky_messages[message.channel.id]
            # Only check the last 3 messages for better performance in high-traffic channels
            async for msg in message.channel.history(limit=3):
                if msg.author == bot.user and msg.content == sticky_message:
                    return  # Sticky message is already present, no need to repost
            # Post sticky message if not found in recent messages
            await message.channel.send(sticky_message)
            print(f"Reposted sticky message in {message.channel.name}.")

        # Constants


# Handle Tebex webhook messages
        if message.webhook_id and message.channel.id == tebex_channel:
            try:
                content = message.content
                print(f"Received webhook content: {content}")

                if "has received a payment" in content:
                    parts = content.split("╽")
                    data = {}
                    
                    # Parse the first part (store name)
                    data['store'] = parts[0].split("has received a payment")[0].strip()
                    
                    # Parse remaining parts
                    for part in parts[1:]:
                        if ":" in part:
                            key, value = part.split(":", 1)
                            key = key.strip().lower()
                            value = value.strip()
                            data[key] = value

                    # Store transaction in MongoDB
                    transaction_data = {
                        'transaction_id': data.get('transaction id', '0'),
                        'buyer': data.get('from', 'N/A'),
                        'item': data.get('package', 'N/A'),
                        'price': data.get('price', 'N/A'),
                        'email': data.get('email', 'N/A'),
                        'timestamp': datetime.now(timezone.utc).isoformat(),
                        'chargeback': False
                    }
                    
                    if save_transaction(transaction_data):
                        print(f"Transaction {transaction_data['transaction_id']} saved to MongoDB")
                    else:
                        print(f"Failed to save transaction {transaction_data['transaction_id']} to MongoDB")

                    # Create embed for notification
                    embed = discord.Embed(
                        title="Purchase Notification",
                        description=f"Your Store has received a payment | From: {data.get('from', 'Unknown')} | Price: {data.get('price', '$0')} | Package: {data.get('package', 'Unknown')} | Transaction ID: {data.get('transaction id', '0')} | Email: {data.get('email', 'No Email')}",
                        color=0x2B2D31,
                        timestamp=current_time
                    )

                    # Set timestamp in the footer
                    embed.set_footer(
                        text=current_time.strftime("%m/%d/%Y %I:%M %p"),
                        icon_url=message.guild.icon.url if message.guild.icon else None
                    )

                        # Try to delete the webhook message after sending the formatted one
                    try:
                        await message_to_delete.delete()
                    except discord.NotFound:
                        print("Webhook message already deleted or not found")
                    except Exception as e:
                        print(f"Failed to delete webhook message: {e}")

                    # Send the formatted message
                    channel = bot.get_channel(tebex_channel)
                    if channel:
                        await channel.send(embed=embed)
                        print("Successfully sent purchase notification")
                    else:
                        print(f"Could not find channel with ID {tebex_channel}")
                    
            except Exception as e:
                print(f"Error processing webhook message: {e}")
                traceback.print_exc()
        
        # Process commands after webhook handling
        await bot.process_commands(message)
        
    except Exception as e:
        print(f"Error in on_message event: {e}")
        traceback.print_exc()


# Sticky Message System
@tree.command(name="set_sticky_message", description="Set a sticky message in a channel")
@app_commands.default_permissions(administrator=True)
async def set_sticky_message(interaction: discord.Interaction, channel: discord.TextChannel, message: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        global sticky_messages
        sticky_messages[channel.id] = message
        
        # Delete any existing sticky messages
        async for msg in channel.history(limit=10):
            if msg.author == bot.user and msg.content == message:
                try:
                    await msg.delete()
                    await asyncio.sleep(0.5)  # Add a small delay between operations
                except Exception as e:
                    print(f"Error deleting existing sticky message: {e}")
        
        # Post the new sticky message immediately
        try:
            await channel.send(message)
            await interaction.response.send_message(f"Sticky message set in {channel.mention}: {message}")
        except Exception as e:
            print(f"Error setting sticky message: {e}")
            await interaction.response.send_message("Failed to set sticky message. Please try again.")
    except Exception as e:
        print(f"Error in set_sticky_message: {e}")
        await interaction.response.send_message("An error occurred while setting the sticky message.")

@bot.event
async def on_message(message):
    try:
        if message.author == bot.user:
            return

        await bot.process_commands(message)

        # Handle sticky messages
        if message.channel.id in sticky_messages:
            sticky_message = sticky_messages[message.channel.id]
            # First delete any existing sticky messages
            async for msg in message.channel.history(limit=10):
                if msg.author == bot.user and msg.content == sticky_message:
                    try:
                        await msg.delete()
                        await asyncio.sleep(0.5)  # Add a small delay between operations
                    except Exception as e:
                        print(f"Error deleting sticky message: {e}")
            # Then post the new sticky message
            try:
                await message.channel.send(sticky_message)
                print(f"Reposted sticky message in {message.channel.name}.")
            except Exception as e:
                print(f"Error posting sticky message: {e}")

        # Handle Tebex webhook messages
        if message.webhook_id and message.channel.id == tebex_channel:
            try:
                content = message.content
                print(f"Received webhook content: {content}")

                if "has received a payment" in content:
                    parts = content.split("╽")
                    data = {}
                    
                    # Parse the first part (store name)
                    data['store'] = parts[0].split("has received a payment")[0].strip()
                    
                    # Parse remaining parts
                    for part in parts[1:]:
                        if ":" in part:
                            key, value = part.split(":", 1)
                            key = key.strip().lower()
                            value = value.strip()
                            data[key] = value

                    # Get current time
                    current_time = datetime.now(timezone.utc)

                        # Store transaction in MongoDB
                    transaction_data = {
                        'buyer': data.get('from', 'Unknown'),
                        'item': data.get('package', 'Unknown'),
                        'price': data.get('price', '$0'),
                        'email': data.get('email', 'No Email'),
                        'timestamp': current_time,
                        'transaction_id': data.get('transaction id', '0'),
                        'chargeback': False
                    }
                    
                    # Save transaction to MongoDB using the imported function
                    save_transaction(transaction_data)

                    # Create and send embed
                    embed = discord.Embed(
                        title="Purchase Notification",
                        color=0x2B2D31,
                        timestamp=current_time
                    )

                    # Create compact description with all fields in one line
                    field_value = (
                        f"From: {data.get('from', 'Unknown')} | "
                        f"Price: {data.get('price', '$0')} | "
                        f"Package: {data.get('package', 'Unknown')} | "
                        f"Transaction ID: {data.get('transaction id', '0')} | "
                        f"Email: {data.get('email', 'No Email')}"
                    )
                    embed.description = field_value

                    # Set footer with timestamp
                    embed.set_footer(
                        text=current_time.strftime("%m/%d/%Y %I:%M %p"),
                        icon_url=message.guild.icon.url if message.guild.icon else None
                    )

                    # Use rate-limited delete
                    await delete_message_with_backoff(message)

                    # Send the formatted message
                    channel = bot.get_channel(tebex_channel)
                    if channel:
                        await channel.send(embed=embed)
                        print("Successfully sent purchase notification")
                    else:
                        print(f"Could not find channel with ID {tebex_channel}")

            except Exception as e:
                print(f"Error processing webhook message: {e}")
                traceback.print_exc()

    except Exception as e:
        print(f"Error in on_message event: {e}")
        traceback.print_exc()


@tree.command(name="remove_sticky_message", description="Remove the sticky message from a channel")
@app_commands.default_permissions(administrator=True)
async def remove_sticky_message(interaction: discord.Interaction, channel: discord.TextChannel):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    print("Remove sticky message command invoked")  # Debugging output
    global sticky_messages
    if channel.id in sticky_messages:
        del sticky_messages[channel.id]
        await interaction.response.send_message(f"Sticky message removed from {channel.mention}.")
    else:
        await interaction.response.send_message(f"No sticky message found in {channel.mention}.")       

# Load or initialize JSON files
def load_json(file_path):
    try:
        if not os.path.exists(file_path):
            # Create file with empty dictionary if it doesn't exist
            with open(file_path, 'w') as f:
                json.dump({}, f)
            return {}
            
        with open(file_path, 'r') as f:
            content = f.read()
            if not content:  # If file is empty
                return {}
            return json.loads(content)
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        return {}

def save_json(file_path, data):
    try:
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=4)
        return True
    except Exception as e:
        print(f"Error saving JSON file: {e}")
        return False


# Log purchase events from webhook




# Reaction Role System
@tree.command(name="setup_reaction_roles", description="Set up reaction roles with a custom message")
@app_commands.default_permissions(administrator=True)
async def setup_reaction_roles(
    interaction: discord.Interaction, 
    channel: discord.TextChannel, 
    message: str,
    allow_multiple: bool = True
):
    if not log_permission_check(interaction, "setup_reaction_roles"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    """Set up reaction roles in a channel with a custom message"""
    global reaction_message_id, reaction_channel_id, reaction_roles
    
    try:
        # Create and send the initial embed
        embed = discord.Embed(
            title="Reaction Roles",
            description=message,
            color=discord.Color.blue()
        )
        msg = await channel.send(embed=embed)
        
        # Update global variables
        reaction_message_id = msg.id
        reaction_channel_id = channel.id
        reaction_roles = {
            "config": {
                "allow_multiple": allow_multiple
            },
            "roles": {}
        }
        
        # Send instructions
        await interaction.response.send_message(
            "Now, let's add roles. Send messages in this format:\n"
            "`emoji @role`\n"
            "Example: 🎮 @Gamer\n"
            "Type 'done' when finished."
        )
        
        def message_check(m):
            return m.author == interaction.user and m.channel == interaction.channel and (m.content.lower() == 'done' or (len(m.content.split()) == 2 and len(m.role_mentions) > 0))

        # Wait for role additions
        while True:
            try:
                response = await bot.wait_for('message', timeout=300.0, check=message_check)
                
                if response.content.lower() == 'done':
                    await interaction.followup.send("Reaction role setup complete!")
                    break
                
                # Parse emoji and role from response
                parts = response.content.split()
                if len(parts) != 2:
                    await interaction.followup.send("Invalid format. Please use: `emoji @role`")
                    continue
                
                emoji = parts[0]
                role_id = int(parts[1].strip('<@&>'))
                role = interaction.guild.get_role(role_id)
                
                if not role:
                    await interaction.followup.send("Invalid role. Please try again.")
                    continue
                
                # Update embed with new role
                embed.add_field(name=f"{emoji} {role.name}", value="React to get this role!", inline=False)
                await msg.edit(embed=embed)
                
                # Add to reaction_roles and add reaction
                reaction_roles["roles"][emoji] = role.id
                await msg.add_reaction(emoji)
                await save_data()
                await response.add_reaction('✅')
                
            except asyncio.TimeoutError:
                await interaction.followup.send("Setup timed out. The message has been created with any roles that were added.")
                break
            except Exception as e:
                await interaction.followup.send(f"Error adding role: {str(e)}")
                continue

    except discord.Forbidden:
        await interaction.response.send_message("I don't have the required permissions to set up reaction roles.", ephemeral=True)
    except Exception as e:
        await interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)


@bot.event
async def on_raw_reaction_add(payload):
    # Ignore bot reactions
    if payload.user_id == bot.user.id:
        return

    # Check if this is a reaction role message
    if payload.message_id == reaction_message_id:
        emoji = str(payload.emoji)
        # Check if reaction_roles has the expected structure
        if "roles" in reaction_roles and emoji in reaction_roles["roles"]:
            guild = bot.get_guild(payload.guild_id)
            if not guild:
                return

            role = guild.get_role(reaction_roles["roles"][emoji])
            if not role:
                return

            member = guild.get_member(payload.user_id)
            if not member:
                return

            try:
                # If multiple roles are not allowed, remove other reaction roles first
                if "config" in reaction_roles and not reaction_roles["config"]["allow_multiple"]:
                    for role_id in reaction_roles["roles"].values():
                        if role_id != reaction_roles["roles"][emoji]:
                            old_role = guild.get_role(role_id)
                            if old_role and old_role in member.roles:
                                await member.remove_roles(old_role)
                
                await member.add_roles(role)
            except discord.HTTPException:
                pass

@bot.event
async def on_raw_reaction_remove(payload):
    # Check if this is a reaction role message
    if payload.message_id == reaction_message_id:
        emoji = str(payload.emoji)
        # Check if reaction_roles has the expected structure
        if "roles" in reaction_roles and emoji in reaction_roles["roles"]:
            guild = bot.get_guild(payload.guild_id)
            if not guild:
                return

            role = guild.get_role(reaction_roles["roles"][emoji])
            if not role:
                return

            member = guild.get_member(payload.user_id)
            if not member:
                return

            try:
                await member.remove_roles(role)
            except discord.HTTPException:
                pass

# Tebex-related commands
@tree.command(name="validate_purchase", description="Validate a purchase by transaction ID")
@app_commands.default_permissions(administrator=True)
async def validate_purchase(interaction: discord.Interaction, transaction_id: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    transactions = load_json(TRANSACTIONS_FILE)
    purchase_details = transactions.get(transaction_id)
    
    if purchase_details:
        # Determine embed color based on chargeback status
        is_chargeback = purchase_details.get('chargeback', False)
        color = discord.Color.red() if is_chargeback else discord.Color.green()
        
        embed = discord.Embed(
            title="Purchase Verification",
            color=color,
            timestamp=datetime.fromisoformat(purchase_details['timestamp'])
        )
        
        # Add status indicator to title if it's a chargeback
        if is_chargeback:
            embed.title = "Purchase Verification ⚠️ CHARGEBACK"
        
        embed.add_field(name="Transaction ID", value=transaction_id, inline=False)
        embed.add_field(name="Buyer", value=purchase_details['buyer'], inline=True)
        embed.add_field(name="Item", value=purchase_details['item'], inline=True)
        embed.add_field(name="Price", value=purchase_details['price'], inline=True)
        embed.add_field(name="Email", value=purchase_details.get('email', 'Not provided'), inline=False)
        
        # Add status field
        status = "🚫 Marked as Chargeback" if is_chargeback else "✅ Valid Transaction"
        embed.add_field(name="Status", value=status, inline=False)
        
        await interaction.response.send_message(embed=embed)
    else:
        await interaction.response.send_message("Purchase not found.", ephemeral=True)

@tree.command(name="lookup_transaction", description="Lookup a transaction by ID")
@app_commands.default_permissions(administrator=True)
async def lookup_transaction(interaction: discord.Interaction, transaction_id: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Query transaction from MongoDB
        transaction_details = TRANSACTIONS_COLLECTION.find_one({"transaction_id": transaction_id}, {"_id": 0})
        
        if not transaction_details:
            await interaction.response.send_message("Transaction not found.", ephemeral=True)
            return

        # Determine embed color based on chargeback status
        color = discord.Color.red() if transaction_details.get('chargeback', False) else discord.Color.blue()
        
        # Convert timestamp string to datetime object if it's a string
        timestamp = transaction_details['timestamp']
        if isinstance(timestamp, str):
            timestamp = datetime.fromisoformat(timestamp)
        
        embed = discord.Embed(
            title="Transaction Details",
            color=color,
            timestamp=timestamp
        )
        
        # Add status indicator to title if it's a chargeback
        if transaction_details.get('chargeback', False):
            embed.title = "Transaction Details ⚠️ CHARGEBACK"
        
        embed.add_field(name="Transaction ID", value=transaction_id, inline=False)
        embed.add_field(name="Buyer", value=transaction_details.get('buyer', 'N/A'), inline=True)
        embed.add_field(name="Item", value=transaction_details.get('item', 'N/A'), inline=True)
        embed.add_field(name="Price", value=transaction_details.get('price', 'N/A'), inline=True)
        if 'email' in transaction_details:
            embed.add_field(name="Email", value=transaction_details['email'], inline=False)
        
        # Add chargeback status field
        status = "🚫 Chargeback" if transaction_details.get('chargeback', False) else "✅ Valid"
        embed.add_field(name="Status", value=status, inline=False)
        
        await interaction.response.send_message(embed=embed)
    except Exception as e:
        logging.error(f"Error in lookup_transaction: {e}")
        await interaction.response.send_message("An error occurred while looking up the transaction.", ephemeral=True)

@tree.command(name="markchargeback", description="Mark a transaction as a chargeback")
@app_commands.default_permissions(administrator=True)
async def markchargeback(interaction: discord.Interaction, transaction_id: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Ensure MongoDB connection is available
        if not TRANSACTIONS_COLLECTION:
            raise Exception("Database connection not available")
    except Exception as e:
        await interaction.response.send_message(f"Error: {str(e)}", ephemeral=True)
        return

    try:
        # Find and update the transaction in MongoDB
        result = TRANSACTIONS_COLLECTION.find_one_and_update(
            {"transaction_id": transaction_id},
            {"$set": {"chargeback": True}},
            return_document=pymongo.ReturnDocument.AFTER
        )
        
        if not result:
            await interaction.response.send_message("Transaction not found.", ephemeral=True)
            return
        
        embed = discord.Embed(
            title="Transaction Marked as Chargeback",
            description=f"Transaction ID: {transaction_id} has been marked as a chargeback.",
            color=discord.Color.red(),
            timestamp=datetime.now(timezone.utc)
        )
        
        embed.add_field(name="Buyer", value=result.get('buyer', 'N/A'), inline=True)
        embed.add_field(name="Item", value=result.get('item', 'N/A'), inline=True)
        embed.add_field(name="Price", value=result.get('price', 'N/A'), inline=True)
        if 'email' in result:
            embed.add_field(name="Email", value=result['email'], inline=False)
        
        await interaction.response.send_message(embed=embed)
        
    except pymongo.errors.PyMongoError as e:
        logging.error(f"MongoDB error in markchargeback command: {e}")
        await interaction.response.send_message(
            "A database error occurred. Please try again later.",
            ephemeral=True
        )
    except Exception as e:
        logging.error(f"Error in markchargeback command: {e}")
        await interaction.response.send_message(
            "An error occurred while processing the command. Please try again later.",
            ephemeral=True
        )

# Set Tebex webhook channel
@tree.command(name="set_tebex_channel", description="Set up Tebex notification channels")
@app_commands.default_permissions(administrator=True)
async def set_tebex_channel(
    interaction: discord.Interaction, 
    tebex_webhook: str,
    output_channel: discord.TextChannel
):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    """
    Set up Tebex channels:
    tebex_webhook: Discord webhook URL where Tebex will send notifications
    output_channel: Channel where formatted purchase messages will be sent
    """
    global tebex_channel, webhook_url
    
    try:
        # Validate webhook URL format
        if not tebex_webhook.startswith('https://discord.com/api/webhooks/'):
            await interaction.response.send_message(
                "Invalid webhook URL. Please provide a valid Discord webhook URL.", 
                ephemeral=True
            )
            return

        # Store the webhook URL and channel ID
        webhook_url = tebex_webhook
        tebex_channel = output_channel.id
        
        # Create embed with setup information
        embed = discord.Embed(
            title="Tebex Channel Setup",
            description="Configuration completed successfully!",
            color=discord.Color.green(),
            timestamp=datetime.now(timezone.utc)
        )
        
        embed.add_field(
            name="Webhook Status", 
            value="✅ Webhook URL configured", 
            inline=True
        )
        
        embed.add_field(
            name="Output Channel", 
            value=output_channel.mention, 
            inline=True
        )
        
        # Add Tebex setup instructions
        embed.add_field(
            name="Tebex Setup Instructions",
            value=(
                "1. Go to your Tebex dashboard\n"
                "2. Navigate to Webhooks settings\n"
                "3. Add a new webhook\n"
                "4. Paste the webhook URL you provided\n"
                "5. In the notification format, use this exact structure:"
            ),
            inline=False
        )

        # Add notification structure
        embed.add_field(
            name="Required Notification Structure",
            value=(
                "```\n"
                "{webstore} has received a payment ╽ "
                "From: {username} ╽ "
                "Price: {price} ╽ "
                "Package: {packagename} ╽ "
                "Transaction ID: {transactionid} ╽ "
                "Email: {email}\n"
                "```\n"
                "⚠️ The structure must match exactly, including the `╽` symbols!"
            ),
            inline=False
        )
        
        # Save the settings
        await save_data()
        
        # Send the confirmation embed
        await interaction.response.send_message(embed=embed, ephemeral=True)
        
    except Exception as e:
        await interaction.response.send_message(
            f"An error occurred: {str(e)}", 
            ephemeral=True
        )

# Add purchase with improved formatting and validation
@tree.command(name="add_purchase", description="Add a purchase to the database")
@app_commands.default_permissions(administrator=True)
async def add_purchase(interaction: discord.Interaction, transaction_id: str, buyer: str, item: str, price: str, email: str = None):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    if not tebex_channel:
        await interaction.response.send_message("Tebex system is not configured. Please use /set_tebex_channel first.", ephemeral=True)
        return

    # Create transaction document
    transaction = {
        'transaction_id': transaction_id,
        'buyer': buyer,
        'item': item,
        'price': price,
        'email': email,
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'chargeback': False
    }
    
    try:
        # Insert into MongoDB
        TRANSACTIONS_COLLECTION.insert_one(transaction)
        
        embed = discord.Embed(
            title="Purchase Added Successfully",
            color=discord.Color.green(),
            timestamp=datetime.now(timezone.utc)
        )
        embed.add_field(name="Transaction ID", value=transaction_id, inline=False)
        embed.add_field(name="Buyer", value=buyer, inline=True)
        embed.add_field(name="Item", value=item, inline=True)
        embed.add_field(name="Price", value=price, inline=True)
        if email:
            embed.add_field(name="Email", value=email, inline=False)
        
        await interaction.response.send_message(embed=embed)
    except pymongo.errors.DuplicateKeyError:
        await interaction.response.send_message("Transaction ID already exists!", ephemeral=True)
    except Exception as e:
        logging.error(f"Error adding purchase: {e}")
        await interaction.response.send_message("An error occurred while adding the purchase.", ephemeral=True)

# Function to save data

async def save_data():
    global gang_strikes, gang_roles, gang_members, gang_leaders
    global application_forms, application_channel, application_log_channel, sticky_messages
    global welcome_channel_id, welcome_message, welcome_image_url, vanity_url, role_name, notification_channel_id
    global join_role_id, reaction_message_id, reaction_channel_id, emoji_role_map, tebex_channel, webhook_url
    global applications_status

    try:
        # Convert numeric keys to strings in gang data
        processed_gang_roles = {}
        for gang_name, gang_data in gang_roles.items():
            processed_gang_data = gang_data.copy()
            processed_gang_data['leader'] = str(gang_data['leader'])
            processed_gang_data['leader_role'] = str(gang_data['leader_role'])
            processed_gang_data['members'] = [str(member_id) for member_id in gang_data['members']]
            processed_gang_roles[gang_name] = processed_gang_data

        processed_gang_leaders = {str(k): str(v) for k, v in gang_leaders.items()}
        processed_gang_members = {str(k): v for k, v in gang_members.items()}
        processed_gang_strikes = {str(k): v for k, v in gang_strikes.items()}

        data = {
            "gangs": {
                "roles": processed_gang_roles,
                "members": processed_gang_members,
                "leaders": processed_gang_leaders,
                "strikes": processed_gang_strikes
            },
            "applications": {
                "forms": application_forms,
                "channels": {
                    "application_channel": application_channel,
                    "log_channel": application_log_channel
                },
                "status": applications_status
            },
            "settings": {
                "welcome": {
                    "channel_id": welcome_channel_id,
                    "message": welcome_message,
                    "image_url": welcome_image_url
                },
                "vanity": {
                    "url": vanity_url,
                    "role_name": role_name
                },
                "notifications": {
                    "channel_id": notification_channel_id
                },
                "join_role_id": join_role_id
            },
            "sticky_messages": sticky_messages,
            "tebex_settings": {
                "channel_id": tebex_channel,
                "webhook_url": webhook_url
            },
            "reaction_roles": {
                "roles": reaction_roles,
                "message_id": reaction_message_id,
                "channel_id": reaction_channel_id
            }
        }
        
        # Save data to MongoDB
        from database import save_data as db_save_data
        success = await db_save_data(data)
        
        if success:
            logging.info("Data saved successfully to MongoDB")
        else:
            logging.error("Failed to save data to MongoDB")
            
    except Exception as e:
        logging.error(f"Error saving data: {e}")


# Function to load data on startup
async def load_data():
    global gang_strikes, gang_roles, gang_members, gang_leaders
    global application_forms, application_channel, application_log_channel, sticky_messages
    global welcome_channel_id, welcome_message, welcome_image_url, vanity_url, role_name, notification_channel_id
    global join_role_id, reaction_message_id, reaction_channel_id, emoji_role_map, tebex_channel, webhook_url
    global applications_status

    print("Loading data...")  # Debugging start
    try:
        # Connect to MongoDB
        client = pymongo.MongoClient("mongodb://localhost:27017/")
        db = client["missminutesbot"]
        
        # Load data from MongoDB collections
        data = {}
        
        # Load gangs data
        gangs_data = db["gangs"].find_one({"_id": "gangs"})
        if gangs_data:
            data["gangs"] = {k: v for k, v in gangs_data.items() if k != "_id"}
        
        # Load applications data
        applications_data = db["applications"].find_one({"_id": "applications"})
        if applications_data:
            data["applications"] = {k: v for k, v in applications_data.items() if k != "_id"}
            
        # Load settings data
        settings_data = db["settings"].find_one({"_id": "settings"})
        if settings_data:
            data["settings"] = {k: v for k, v in settings_data.items() if k != "_id"}
            
        # Load sticky messages
        sticky_data = db["sticky_messages"].find_one({"_id": "sticky_messages"})
        if sticky_data:
            data["sticky_messages"] = sticky_data.get("messages", {})
            
        # Load tebex settings
        tebex_data = db["tebex_settings"].find_one({"_id": "tebex_settings"})
        if tebex_data:
            data["tebex_settings"] = {k: v for k, v in tebex_data.items() if k != "_id"}
            
        # Load reaction roles
        reaction_data = db["reaction_roles"].find_one({"_id": "reaction_roles"})
        if reaction_data:
            data["reaction_roles"] = {k: v for k, v in reaction_data.items() if k != "_id"}
            
        print("Data loaded from MongoDB.")  # Debugging line

        # Load gang data with proper type conversion
        gangs_data = data.get("gangs", {})
        gang_roles_data = gangs_data.get("roles", {})
        
        # Convert gang_roles data
        gang_roles = {}
        for gang_name, gang_data in gang_roles_data.items():
            gang_roles[gang_name] = {
                "leader": int(gang_data["leader"]),
                "leader_role": int(gang_data["leader_role"]),
                "members": [int(m) if isinstance(m, str) and m.isdigit() else m for m in gang_data["members"]],
                "member_limit": gang_data.get("member_limit", 5),
                "current_members": gang_data.get("current_members", 0)
            }
        
        # Convert gang_leaders data
        gang_leaders_data = gangs_data.get("leaders", {})
        gang_leaders = {int(k): int(v) for k, v in gang_leaders_data.items() if k.isdigit() and str(v).isdigit()}
        
        gang_members = gangs_data.get("members", {})
        gang_strikes = gangs_data.get("strikes", {})

        print(f"Loaded gang data: Roles={len(gang_roles)}, Leaders={len(gang_leaders)}, Strikes={len(gang_strikes)}")  # Debug info

        # Load applications
        applications = data.get("applications", {})
        application_forms = applications.get("forms", {})
        application_channel = applications.get("channels", {}).get("application_channel")
        application_log_channel = applications.get("channels", {}).get("log_channel")
        applications_status = applications.get("status", {})

        print(f"Loaded applications: Forms={len(application_forms)}, Status={len(applications_status)}")  # Debug info

        # Load settings
        settings = data.get("settings", {})
        welcome = settings.get("welcome", {})
        welcome_channel_id = welcome.get("channel_id")
        welcome_message = welcome.get("message")
        welcome_image_url = welcome.get("image_url")

        print(f"Loaded welcome settings: ChannelID={welcome_channel_id}")  # Debug info

        vanity = settings.get("vanity", {})
        vanity_url = vanity.get("url")
        role_name = vanity.get("role_name")

        print(f"Loaded vanity settings: URL={vanity_url}, RoleName={role_name}")  # Debug info

        notifications = settings.get("notifications", {})
        notification_channel_id = notifications.get("channel_id")

        print(f"Loaded notification settings: ChannelID={notification_channel_id}")  # Debug info

        join_role_id = settings.get("join_role_id")

        # Load sticky messages
        sticky_messages = data.get("sticky_messages", {})

        print(f"Loaded sticky messages: Count={len(sticky_messages)}")  # Debug info

        # Load Tebex settings
        tebex_settings = data.get("tebex_settings", {})
        tebex_channel = tebex_settings.get("channel_id")
        webhook_url = tebex_settings.get("webhook_url")

        print(f"Loaded Tebex settings: ChannelID={tebex_channel}")  # Debug info

        # Load reaction roles
        reaction_data = data.get("reaction_roles", {})
        global reaction_roles
        reaction_roles = {
            "roles": reaction_data.get("roles", {}),
            "config": reaction_data.get("config", {"allow_multiple": False})
        }
        global reaction_message_id, reaction_channel_id
        reaction_message_id = reaction_data.get("message_id")
        reaction_channel_id = reaction_data.get("channel_id")

        print("Data loaded successfully.")

    except json.JSONDecodeError as e:
        print(f"Error decoding JSON: {e}")
    except Exception as e:
        print(f"Error loading data: {e}")

# Event Handlers Section
@bot.event
async def on_ready():
    print(f'Logged in as {bot.user} (ID: {bot.user.id})')
    print('------')
    
    try:
        # Load ticket data first
        print("Loading ticket configuration...")
        success = await load_ticket_data()
        if not success:
            print("Warning: Failed to load ticket configuration")
            
        # Load rest of data
        print("\nLoading bot data...")
        await load_data()
        print("Bot data loaded successfully")
        
        # Implement retry logic for command sync
        max_retries = 3
        retry_delay = 5  # seconds
        
        for attempt in range(max_retries):
            try:
                print(f"\nStarting command sync process (attempt {attempt + 1}/{max_retries})...")
                synced = await tree.sync()
                print(f"Synced {len(synced)} command(s)!")
                break
            except discord.errors.DiscordServerError as e:
                if attempt < max_retries - 1:
                    print(f"Discord service temporarily unavailable. Retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    print("Failed to sync commands after all retries. Bot will continue with existing commands.")
                    logging.error(f"Command sync failed: {e}")
        
        # Start tasks
        check_vanity_status.start()
        print(f"Started vanity status check task")
        
    except Exception as e:
        print(f"Error during bot initialization: {e}")
        traceback.print_exc()
    
    print(f"Bot initialization completed!")

@bot.event
async def on_message(message):
    try:
        # Ignore messages from the bot itself
        if message.author == bot.user:
            return

        # Process commands first
        await bot.process_commands(message)

        # Handle sticky messages with optimized performance
        if message.channel.id in sticky_messages:
            sticky_message = sticky_messages[message.channel.id]
            # Only check the last 3 messages for better performance in high-traffic channels
            async for msg in message.channel.history(limit=3):
                if msg.author == bot.user and msg.content == sticky_message:
                    return  # Sticky message is already present, no need to repost
            # Post sticky message if not found in recent messages
            await message.channel.send(sticky_message)
            print(f"Reposted sticky message in {message.channel.name}.")

        # Handle Tebex webhook messages
        if message.webhook_id and message.channel.id == tebex_channel:
            try:
                # Store message reference before processing
                message_to_delete = message
                
                # Parse the webhook message
                content = message.content
                print(f"Received webhook content: {content}")

                if "has received a payment" in content:
                    parts = content.split("╽")
                    data = {}
                    
                    # Parse the first part (store name)
                    data['store'] = parts[0].split("has received a payment")[0].strip()
                    
                    # Parse remaining parts
                    for part in parts[1:]:
                        if ":" in part:
                            key, value = part.split(":", 1)
                            key = key.strip().lower()
                            value = value.strip()
                            data[key] = value

                    # Get current time
                    current_time = datetime.now(timezone.utc)

                        # Store transaction in MongoDB
                    transaction_data = {
                        'buyer': data.get('from', 'Unknown'),
                        'item': data.get('package', 'Unknown'),
                        'price': data.get('price', '$0'),
                        'email': data.get('email', 'No Email'),
                        'timestamp': current_time,
                        'transaction_id': data.get('transaction id', '0'),
                        'chargeback': False
                    }
                    
                    # Save transaction to MongoDB using the imported function
                    save_transaction(transaction_data)

                    # Create and send embed
                    embed = discord.Embed(
                        title="Purchase Notification",
                        color=0x2B2D31,
                        timestamp=current_time
                    )

                    # Create compact description with all fields in one line
                    field_value = (
                        f"From: {data.get('from', 'Unknown')} | "
                        f"Price: {data.get('price', '$0')} | "
                        f"Package: {data.get('package', 'Unknown')} | "
                        f"Transaction ID: {data.get('transaction id', '0')} | "
                        f"Email: {data.get('email', 'No Email')}"
                    )
                    embed.description = field_value

                    # Set footer with timestamp
                    embed.set_footer(
                        text=current_time.strftime("%m/%d/%Y %I:%M %p"),
                        icon_url=message.guild.icon.url if message.guild.icon else None
                    )

                        # Try to delete the webhook message after sending the formatted one
                    try:
                        await message_to_delete.delete()
                    except discord.NotFound:
                        print("Webhook message already deleted or not found")
                    except Exception as e:
                        print(f"Failed to delete webhook message: {e}")

                    # Send the formatted message
                    channel = bot.get_channel(tebex_channel)
                    if channel:
                        await channel.send(embed=embed)
                        print("Successfully sent purchase notification")
                    else:
                        print(f"Could not find channel with ID {tebex_channel}")

            except Exception as e:
                print(f"Error processing webhook message: {e}")
                traceback.print_exc()

    except Exception as e:
        print(f"Error in on_message event: {e}")
        traceback.print_exc()

@bot.event
async def on_member_join(member):
    if welcome_channel_id:
        channel = bot.get_channel(welcome_channel_id)
        if channel:
            embed = discord.Embed(
                title="Welcome!",
                description=welcome_message,
                color=discord.Color.blue()
            )
            if welcome_image_url:
                embed.set_image(url=welcome_image_url)
            await channel.send(embed=embed)

    if join_role_id:
        role = member.guild.get_role(join_role_id)
        if role:
            await member.add_roles(role)
            print(f"Assigned join role '{role.name}' to {member.name}.")

@bot.event
async def on_raw_reaction_add(payload):
    # Ignore bot reactions
    if payload.user_id == bot.user.id:
        return

    # Check if this is a reaction role message
    if payload.message_id == reaction_message_id:
        emoji = str(payload.emoji)
        # Check if reaction_roles has the expected structure
        if "roles" in reaction_roles and emoji in reaction_roles["roles"]:
            guild = bot.get_guild(payload.guild_id)
            if not guild:
                return

            role = guild.get_role(reaction_roles["roles"][emoji])
            if not role:
                return

            member = guild.get_member(payload.user_id)
            if not member:
                return

            try:
                # If multiple roles are not allowed, remove other reaction roles first
                if "config" in reaction_roles and not reaction_roles["config"]["allow_multiple"]:
                    for role_id in reaction_roles["roles"].values():
                        if role_id != reaction_roles["roles"][emoji]:
                            old_role = guild.get_role(role_id)
                            if old_role and old_role in member.roles:
                                await member.remove_roles(old_role)
                
                await member.add_roles(role)
            except discord.HTTPException:
                pass

@bot.event
async def on_raw_reaction_remove(payload):
    # Check if this is a reaction role message
    if payload.message_id == reaction_message_id:
        emoji = str(payload.emoji)
        # Check if reaction_roles has the expected structure
        if "roles" in reaction_roles and emoji in reaction_roles["roles"]:
            guild = bot.get_guild(payload.guild_id)
            if not guild:
                return

            role = guild.get_role(reaction_roles["roles"][emoji])
            if not role:
                return

            member = guild.get_member(payload.user_id)
            if not member:
                return

            try:
                await member.remove_roles(role)
            except discord.HTTPException:
                pass
async def restore_application_buttons():
    if application_log_channel:
        channel = bot.get_channel(application_log_channel)
        if channel:
            for user_id, status in applications_status.items():
                if not status.get("responded", False):
                    try:
                        message = await channel.fetch_message(status["message_id"])
                        if message:
                            view = View()
                            accept_button = Button(label="✅ Accept", style=discord.ButtonStyle.green)
                            reject_button = Button(label="❌ Reject", style=discord.ButtonStyle.red)
                            accept_reason_button = Button(label="📝 Accept with Reason", style=discord.ButtonStyle.blurple)
                            reject_reason_button = Button(label="📝 Reject with Reason", style=discord.ButtonStyle.gray)

                            user = bot.get_user(int(user_id))
                            app_name = status["application_name"]

                            async def accept_callback(i):
                                if applications_status.get(user.id, {}).get("responded"):
                                    await i.response.send_message("This application has already been responded to.", ephemeral=True)
                                    return
                                applications_status[user.id] = {"responded": True, "admin": i.user.id, "status": "accepted"}
                                await save_data()
                                await i.response.send_message("Application accepted!", ephemeral=True)
                                await user.send(f"Your application for '{app_name}' has been accepted!")

                            async def reject_callback(i):
                                if applications_status.get(user.id, {}).get("responded"):
                                    await i.response.send_message("This application has already been responded to.", ephemeral=True)
                                    return
                                applications_status[user.id] = {"responded": True, "admin": i.user.id, "status": "rejected"}
                                await save_data()
                                await i.response.send_message("Application rejected!", ephemeral=True)
                                await user.send(f"Your application for '{app_name}' has been rejected.")

                            async def accept_with_reason_callback(i):
                                if applications_status.get(user.id, {}).get("responded"):
                                    await i.response.send_message("This application has already been responded to.", ephemeral=True)
                                    return
                                await i.response.send_modal(AcceptReasonModal(user, app_name))

                            async def reject_with_reason_callback(i):
                                if applications_status.get(user.id, {}).get("responded"):
                                    await i.response.send_message("This application has already been responded to.", ephemeral=True)
                                    return
                                await i.response.send_modal(RejectReasonModal(user, app_name))

                            accept_button.callback = accept_callback
                            reject_button.callback = reject_callback
                            accept_reason_button.callback = accept_with_reason_callback
                            reject_reason_button.callback = reject_with_reason_callback

                            view.add_item(accept_button)
                            view.add_item(reject_button)
                            view.add_item(accept_reason_button)
                            view.add_item(reject_reason_button)

                            await message.edit(view=view)
                    except discord.NotFound:
                        print(f"Could not find message for application {status['message_id']}")
                    except Exception as e:
                        print(f"Error restoring application buttons: {e}")


async def create_backup():

    try:
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        backup_path = f"backups/backup_{timestamp}.json"
        
        # Create backups directory if it doesn't exist
        os.makedirs("backups", exist_ok=True)
        
        with open(DATA_FILE_PATH, 'r') as source:
            data = json.load(source)
            
        with open(backup_path, 'w') as backup:
            json.dump(data, backup, indent=4)
            
        logging.info(f"Backup created: {backup_path}")
    except Exception as e:
        logging.error(f"Backup failed: {e}")

@bot.event
async def on_interaction(interaction: discord.Interaction):
    if interaction.type == discord.InteractionType.component:
        try:
            custom_id = interaction.data.get("custom_id", "")
            
            if custom_id == "create_ticket":
                try:
                    # Create category selection buttons
                    view = View()
                    
                    # Get available categories and ensure it exists
                    categories = ticket_config.get("categories", {})
                    if not categories:
                        await interaction.response.send_message(
                            "No ticket categories are configured! Please contact an administrator.", 
                            ephemeral=True
                        )
                        return
                    
                    # Debug log
                    print(f"Available categories when creating buttons: {categories}")
                    
                    # Create a button for each category
                    for category_id, category_info in categories.items():
                        button = Button(
                            label=category_info["name"],
                            # Store category_id as string in custom_id
                            custom_id=f"ticket_category_{str(category_id)}",
                            style=discord.ButtonStyle.primary
                        )
                        view.add_item(button)
                    
                    # Create an embed for category selection
                    embed = discord.Embed(
                        title="🎫 Create a Ticket",
                        description="Please select a category for your ticket:",
                        color=discord.Color.blue()
                    )
                    
                    # Add category descriptions to embed
                    for category_id, category_info in categories.items():
                        embed.add_field(
                            name=category_info["name"],
                            value=category_info.get("description", "No description available"),
                            inline=False
                        )
                    
                    await interaction.response.send_message(
                        embed=embed,
                        view=view,
                        ephemeral=True
                    )
                    
                except Exception as e:
                    print(f"Error creating ticket selection: {e}")
                    await interaction.response.send_message(
                        "An error occurred while creating the ticket selection. Please try again later.",
                        ephemeral=True
                    )
                
            elif custom_id.startswith("ticket_category_"):
                try:
                    # Extract category_id as string
                    category_id = custom_id.split("ticket_category_")[-1]
                    
                    # Debug logging
                    print(f"Processing ticket category: {category_id}")
                    print(f"Available categories: {ticket_config.get('categories', {})}")
                    
                    # Convert both to strings for comparison
                    categories = {str(k): v for k, v in ticket_config.get("categories", {}).items()}
                    
                    # Verify category exists
                    if category_id not in categories:
                        print(f"Category {category_id} not found in config")
                        await interaction.response.send_message(
                            "This ticket category no longer exists. Please contact an administrator.",
                            ephemeral=True
                        )
                        return
                    
                    # Convert to int for create_ticket function
                    await create_ticket(interaction, int(category_id))
                    return
                    
                except ValueError as e:
                    print(f"Error parsing category ID: {e}")
                    await interaction.response.send_message("Invalid ticket category format.", ephemeral=True)
                    return
                except Exception as e:
                    print(f"Error creating ticket: {e}")
                    await interaction.response.send_message("An error occurred while creating the ticket.", ephemeral=True)
                    return
                    
            elif custom_id == "close_ticket":
                try:
                    await interaction.response.defer(ephemeral=True)
                    success, error = await close_ticket(interaction.channel_id, closer=interaction.user)
                    if not success:
                        await interaction.followup.send(f"Error closing ticket: {error}", ephemeral=True)
                except Exception as e:
                    print(f"Error closing ticket: {e}")
                    try:
                        await interaction.followup.send(
                            "An error occurred while closing the ticket. Please try again.",
                            ephemeral=True
                        )
                    except discord.NotFound:
                        pass

            elif custom_id == "close_with_reason":
                try:
                    modal = discord.ui.Modal(title="Close Ticket")
                    modal.add_item(
                        discord.ui.TextInput(
                            label="Reason",
                            style=discord.TextStyle.paragraph,
                            placeholder="Please provide a reason for closing this ticket..."
                        )
                    )
                    
                    async def modal_callback(m_i: discord.Interaction):
                        try:
                            await m_i.response.defer(ephemeral=True)
                            reason = modal.children[0].value
                            success, error = await close_ticket(m_i.channel_id, reason=reason, closer=m_i.user)
                            if not success:
                                try:
                                    await m_i.followup.send(
                                        f"Error closing ticket: {error}",
                                        ephemeral=True
                                    )
                                except discord.NotFound:
                                    # Channel might be already deleted
                                    pass
                        except Exception as e:
                            print(f"Error in close ticket modal: {e}")
                            try:
                                await m_i.followup.send(
                                    "An error occurred while closing the ticket.",
                                    ephemeral=True
                                )
                            except discord.NotFound:
                                pass
                    
                    modal.on_submit = modal_callback
                    await interaction.response.send_modal(modal)
                except Exception as e:
                    print(f"Error showing close reason modal: {e}")
                    await interaction.response.send_message("An error occurred while processing your request.", ephemeral=True)

            elif custom_id == "reopen_ticket":
                try:
                    await interaction.response.defer(ephemeral=True)
                    
                    # Check if user has staff role
                    has_staff_role = False
                    for role_id in ticket_config["staff_roles"]:
                        role = interaction.guild.get_role(role_id)
                        if role and role in interaction.user.roles:
                            has_staff_role = True
                            break
                    
                    if not has_staff_role:
                        await interaction.followup.send("You don't have permission to reopen tickets.", ephemeral=True)
                        return
                            
                    success, error = await reopen_ticket(interaction.channel_id, interaction.user)
                    if not success:
                        await interaction.followup.send(f"Error reopening ticket: {error}", ephemeral=True)

                            
                except Exception as e:
                    print(f"Error reopening ticket: {e}")
                    try:
                        await interaction.followup.send(
                            "An error occurred while reopening the ticket. Please try again.",
                            ephemeral=True
                        )
                    except discord.NotFound:
                        pass
   
            elif custom_id == "claim_ticket":
                try:
                    # Check if user has staff role
                    has_staff_role = False
                    for role_id in ticket_config["staff_roles"]:
                        role = interaction.guild.get_role(role_id)
                        if role and role in interaction.user.roles:
                            has_staff_role = True
                            break
                    
                    if not has_staff_role:
                        await interaction.response.send_message("You don't have permission to claim tickets.", ephemeral=True)
                        return
                        
                    success, error = await claim_ticket(interaction.channel_id, interaction.user)
                    if not success:
                        await interaction.response.send_message(f"Error claiming ticket: {error}", ephemeral=True)
                    else:
                        await interaction.response.send_message("Ticket claimed successfully!", ephemeral=True)
                except Exception as e:
                    print(f"Error claiming ticket: {e}")
                    await interaction.response.send_message("An error occurred while claiming the ticket.", ephemeral=True)

        except Exception as e:
            print(f"Error in interaction handler: {e}")
            try:
                await interaction.response.send_message("An error occurred while processing your request.", ephemeral=True)
            except:
                pass

bot.run(token)
