#!/usr/bin/env python3
"""
Debug script to check ticket configuration and identify issues
"""

import pymongo
import json
from pprint import pprint

def check_mongodb_connection():
    """Check MongoDB connection and ticket configuration"""
    try:
        # Connect to MongoDB
        mongo_client = pymongo.MongoClient('mongodb://localhost:27017/')
        db = mongo_client['missminutesbot']
        ticket_config_collection = db['ticket_config']
        
        print("🔍 MongoDB Connection Test")
        print("=" * 50)
        
        # Test connection
        mongo_client.admin.command('ping')
        print("✅ MongoDB connection successful")
        
        # Check ticket configuration
        print("\n📋 Ticket Configuration Analysis")
        print("=" * 50)
        
        # Find ticket config
        config_data = ticket_config_collection.find_one({"_id": "ticket_config"})
        
        if config_data:
            print("✅ Ticket configuration found in database")
            
            # Check main config
            if 'config' in config_data:
                config = config_data['config']
                print(f"\n📊 Configuration Summary:")
                print(f"   - Categories: {len(config.get('categories', {}))}")
                print(f"   - Ticket Channel: {config.get('ticket_channel', 'Not set')}")
                print(f"   - Staff Roles: {len(config.get('staff_roles', []))}")
                print(f"   - Last Ticket Number: {config.get('last_ticket_number', 0)}")
                
                # Check categories in detail
                categories = config.get('categories', {})
                if categories:
                    print(f"\n🎫 Categories Details:")
                    for cat_id, cat_info in categories.items():
                        print(f"   - ID: {cat_id}")
                        print(f"     Name: {cat_info.get('name', 'No name')}")
                        print(f"     Description: {cat_info.get('description', 'No description')}")
                        print()
                else:
                    print("\n❌ No categories configured!")
                    print("   This is likely why the ticket panel is not working.")
                    print("   You need to add categories using the bot commands.")
                
            else:
                print("❌ No 'config' field found in ticket configuration")
                
            # Check active tickets
            active_tickets = config_data.get('active_tickets', {})
            print(f"\n🎟️ Active Tickets: {len(active_tickets)}")
            
        else:
            print("❌ No ticket configuration found in database")
            print("   This means the ticket system has never been set up.")
            print("   You need to run the setup commands first.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking MongoDB: {e}")
        return False

def check_bot_files():
    """Check if bot files have the necessary imports and functions"""
    print("\n🔍 Bot Files Analysis")
    print("=" * 50)
    
    try:
        # Check if tickets.py exists and has the right functions
        with open('tickets.py', 'r', encoding='utf-8') as f:
            tickets_content = f.read()
        
        checks = [
            ('create_modern_ticket_panel', 'create_modern_ticket_panel' in tickets_content),
            ('auto_recreate_ticket_panels', 'auto_recreate_ticket_panels' in tickets_content),
            ('ModernTicketPanelView', 'class ModernTicketPanelView' in tickets_content),
            ('ModernTicketCategoryButton', 'class ModernTicketCategoryButton' in tickets_content),
            ('bot.add_view', 'bot.add_view(view)' in tickets_content),
        ]
        
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
        
        # Check bot.py for auto-recreation call
        with open('bot.py', 'r', encoding='utf-8') as f:
            bot_content = f.read()
        
        if 'auto_recreate_ticket_panels' in bot_content:
            print("   ✅ bot.py calls auto_recreate_ticket_panels")
        else:
            print("   ❌ bot.py does not call auto_recreate_ticket_panels")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking bot files: {e}")
        return False

def suggest_fixes():
    """Suggest fixes based on the analysis"""
    print("\n🔧 Suggested Fixes")
    print("=" * 50)
    
    print("1. 📋 Set up ticket categories:")
    print("   Use the bot command: /setup_ticket_system")
    print("   Or manually add categories using: /add_category")
    print()
    
    print("2. 🔄 Restart the bot:")
    print("   After adding categories, restart the bot to trigger auto-recreation")
    print()
    
    print("3. 🎫 Set ticket channel:")
    print("   Make sure a ticket channel is configured")
    print("   Use: /set_ticket_channel")
    print()
    
    print("4. 🧪 Test manually:")
    print("   Try creating a panel manually with: /recreate_ticket_panel")

def main():
    """Run all checks"""
    print("🧪 Ticket System Debug Analysis")
    print("=" * 60)
    
    # Check MongoDB
    mongo_ok = check_mongodb_connection()
    
    # Check bot files
    files_ok = check_bot_files()
    
    # Suggest fixes
    suggest_fixes()
    
    print("\n📊 Summary")
    print("=" * 50)
    print(f"MongoDB: {'✅ OK' if mongo_ok else '❌ Issues'}")
    print(f"Bot Files: {'✅ OK' if files_ok else '❌ Issues'}")
    
    if mongo_ok and files_ok:
        print("\n🎉 System appears to be configured correctly!")
        print("If tickets still don't work, try restarting the bot.")
    else:
        print("\n⚠️ Issues found. Please address the problems above.")

if __name__ == "__main__":
    main()
