# Core Discord Bot Dependencies
discord.py>=2.0.0
aiosqlite>=0.19.0
colorama>=0.4.6
requests>=2.31.0
python-dotenv>=1.0.0

# Database Dependencies
pymongo>=4.5.0
zstandard>=0.23.0  # MongoDB compression optimization

# Performance Optimization Dependencies (optional but recommended)
# Install with: pip install -r requirements.txt
psutil>=5.9.0
motor>=3.3.0
pymongo>=4.5.0
memory-profiler>=0.61.0
aiofiles>=23.2.1
cachetools>=5.3.0
prometheus-client>=0.17.1

# Optional: Advanced profiling (uncomment if needed)
# py-spy>=0.3.14
# line-profiler>=4.1.1

# Optional: Redis for distributed caching (uncomment if using Redis)
# redis>=4.6.0
# aioredis>=2.0.1

# Optional: Advanced database features (uncomment if needed)
# asyncpg>=0.28.0  # For PostgreSQL
# aiomysql>=0.2.0  # For MySQL