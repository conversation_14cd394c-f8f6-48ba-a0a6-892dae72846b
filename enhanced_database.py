"""
Enhanced Database Manager with Advanced Connection Pooling and Optimization
Designed for high-load scenarios up to 100,000 concurrent users
"""

import motor.motor_asyncio
import asyncio
import time
import logging
import threading
import weakref
from collections import defaultdict, OrderedDict
from typing import Optional, Dict, Any, List
from datetime import datetime
import json
import hashlib

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('enhanced_database')

class AdvancedConnectionPool:
    """Advanced connection pool with health monitoring and load balancing"""
    
    def __init__(self, mongo_uri: str, db_name: str, max_pool_size: int = 200):
        self.mongo_uri = mongo_uri
        self.db_name = db_name
        self.max_pool_size = max_pool_size
        
        # Connection pool
        self.client = None
        self.db = None
        self.is_connected = False
        
        # Health monitoring
        self.last_health_check = 0
        self.health_check_interval = 30  # seconds
        self.connection_errors = 0
        self.max_connection_errors = 5
        
        # Performance tracking
        self.query_count = 0
        self.total_query_time = 0
        self.slow_queries = []
        self.slow_query_threshold = 1.0  # seconds
        
        # Connection lock
        self.connection_lock = asyncio.Lock()
    
    async def connect(self) -> bool:
        """Establish connection with advanced pooling"""
        async with self.connection_lock:
            if self.is_connected:
                return True
            
            try:
                # Configure connection with extreme high-load settings
                self.client = motor.motor_asyncio.AsyncIOMotorClient(
                    self.mongo_uri,
                    maxPoolSize=self.max_pool_size,  # Maximum connections
                    minPoolSize=50,  # Minimum connections to maintain
                    maxIdleTimeMS=120000,  # Keep idle connections for 2 minutes
                    connectTimeoutMS=10000,  # 10 second connection timeout
                    serverSelectionTimeoutMS=30000,  # 30 second server selection timeout
                    socketTimeoutMS=60000,  # 60 second socket timeout
                    retryWrites=True,  # Enable retryable writes
                    retryReads=True,   # Enable retryable reads
                    w="majority",  # Write concern for durability
                    readPreference="secondaryPreferred",  # Read from secondaries
                    readConcernLevel="majority",  # Read concern
                    compressors=["zstd", "zlib"],  # Enable compression
                    zlibCompressionLevel=6,  # Compression level
                    maxConnecting=10,  # Max concurrent connection attempts
                    heartbeatFrequencyMS=10000,  # Heartbeat frequency
                )
                
                # Test connection
                await asyncio.wait_for(
                    self.client.admin.command('ping'), 
                    timeout=5.0
                )
                
                self.db = self.client[self.db_name]
                self.is_connected = True
                self.connection_errors = 0
                
                logger.debug(f"Connected to MongoDB with pool size {self.max_pool_size}")
                return True
                
            except Exception as e:
                self.connection_errors += 1
                self.is_connected = False
                logger.error(f"Failed to connect to MongoDB: {e}")
                return False
    
    async def ensure_connection(self) -> bool:
        """Ensure connection is healthy"""
        if not self.is_connected:
            return await self.connect()
        
        # Periodic health check
        current_time = time.time()
        if current_time - self.last_health_check > self.health_check_interval:
            try:
                await asyncio.wait_for(
                    self.client.admin.command('ping'), 
                    timeout=2.0
                )
                self.last_health_check = current_time
                return True
            except Exception as e:
                logger.warning(f"Health check failed: {e}")
                self.is_connected = False
                return await self.connect()
        
        return True
    
    async def execute_query(self, operation, *args, **kwargs):
        """Execute query with monitoring and retry logic"""
        if not await self.ensure_connection():
            raise Exception("Database connection failed")
        
        start_time = time.time()
        retry_count = 0
        max_retries = 3
        
        while retry_count < max_retries:
            try:
                result = await operation(*args, **kwargs)
                
                # Track performance
                query_time = time.time() - start_time
                self.query_count += 1
                self.total_query_time += query_time
                
                # Track slow queries
                if query_time > self.slow_query_threshold:
                    self.slow_queries.append({
                        'operation': str(operation),
                        'time': query_time,
                        'timestamp': datetime.now()
                    })
                    # Keep only last 100 slow queries
                    if len(self.slow_queries) > 100:
                        self.slow_queries.pop(0)
                
                return result
                
            except Exception as e:
                retry_count += 1
                if retry_count >= max_retries:
                    logger.error(f"Query failed after {max_retries} retries: {e}")
                    raise e
                
                # Exponential backoff
                await asyncio.sleep(0.1 * (2 ** retry_count))
                
                # Try to reconnect
                self.is_connected = False
                if not await self.ensure_connection():
                    raise e
    
    def get_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics"""
        avg_query_time = (self.total_query_time / max(self.query_count, 1)) * 1000  # ms
        
        return {
            'is_connected': self.is_connected,
            'query_count': self.query_count,
            'avg_query_time_ms': avg_query_time,
            'slow_queries_count': len(self.slow_queries),
            'connection_errors': self.connection_errors,
            'pool_size': self.max_pool_size
        }

class QueryCache:
    """High-performance query result cache with intelligent invalidation"""
    
    def __init__(self, max_size: int = 10000, default_ttl: int = 300):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache = OrderedDict()
        self.ttl_data = {}
        self.access_count = defaultdict(int)
        self.lock = threading.RLock()
        
        # Cache statistics
        self.hits = 0
        self.misses = 0
        self.evictions = 0
    
    def _generate_key(self, collection: str, query: Dict, projection: Dict = None) -> str:
        """Generate cache key from query parameters"""
        key_data = {
            'collection': collection,
            'query': query,
            'projection': projection
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, collection: str, query: Dict, projection: Dict = None) -> Optional[Any]:
        """Get cached query result"""
        key = self._generate_key(collection, query, projection)
        
        with self.lock:
            if key in self.cache:
                current_time = time.time()
                
                # Check if expired
                if key in self.ttl_data and current_time > self.ttl_data[key]:
                    del self.cache[key]
                    del self.ttl_data[key]
                    self.misses += 1
                    return None
                
                # Move to end (LRU)
                self.cache.move_to_end(key)
                self.access_count[key] += 1
                self.hits += 1
                return self.cache[key]
            
            self.misses += 1
            return None
    
    def set(self, collection: str, query: Dict, result: Any, 
            projection: Dict = None, ttl: int = None):
        """Cache query result"""
        key = self._generate_key(collection, query, projection)
        
        with self.lock:
            # Evict if cache is full
            while len(self.cache) >= self.max_size:
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
                if oldest_key in self.ttl_data:
                    del self.ttl_data[oldest_key]
                if oldest_key in self.access_count:
                    del self.access_count[oldest_key]
                self.evictions += 1
            
            # Set cache entry
            self.cache[key] = result
            self.cache.move_to_end(key)
            
            # Set TTL
            if ttl is not None:
                self.ttl_data[key] = time.time() + ttl
            elif self.default_ttl > 0:
                self.ttl_data[key] = time.time() + self.default_ttl
    
    def invalidate_collection(self, collection: str):
        """Invalidate all cache entries for a collection"""
        with self.lock:
            keys_to_remove = []
            for key in self.cache.keys():
                # This is a simplified check - in production you'd want more sophisticated pattern matching
                if collection in key:
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                if key in self.cache:
                    del self.cache[key]
                if key in self.ttl_data:
                    del self.ttl_data[key]
                if key in self.access_count:
                    del self.access_count[key]
    
    def cleanup_expired(self) -> int:
        """Remove expired entries"""
        with self.lock:
            current_time = time.time()
            expired_keys = [
                key for key, expiry in self.ttl_data.items()
                if current_time > expiry
            ]
            
            for key in expired_keys:
                if key in self.cache:
                    del self.cache[key]
                del self.ttl_data[key]
                if key in self.access_count:
                    del self.access_count[key]
            
            return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self.hits + self.misses
        hit_rate = (self.hits / max(total_requests, 1)) * 100
        
        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'hits': self.hits,
            'misses': self.misses,
            'hit_rate': hit_rate,
            'evictions': self.evictions
        }

class EnhancedDatabaseManager:
    """Enhanced database manager for extreme high-load scenarios"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(EnhancedDatabaseManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        # Connection pool
        self.connection_pool = AdvancedConnectionPool(
            mongo_uri="mongodb://localhost:27017/",
            db_name="missminutesbot",
            max_pool_size=200  # High connection limit for 100k users
        )
        
        # Query cache
        self.query_cache = QueryCache(max_size=50000, default_ttl=300)
        
        # Collection references
        self.collections = {}
        
        # Background tasks
        self.cleanup_task = None
        self.running = False

        self._initialized = True

    async def start(self):
        """Start the enhanced database manager"""
        if self.running:
            return

        self.running = True

        # Connect to database
        if not await self.connection_pool.connect():
            raise Exception("Failed to connect to database")

        # Initialize collections
        await self._initialize_collections()

        # Start background cleanup task
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())

        logger.debug("Enhanced database manager started")

    async def ensure_connection(self) -> bool:
        """Ensure database connection is established"""
        try:
            if not self.running:
                await self.start()

            # Test the connection
            if self.connection_pool.client:
                # Try a simple ping operation
                await self.connection_pool.client.admin.command('ping')
                return True
            else:
                return False
        except Exception as e:
            logger.error(f"Database connection check failed: {e}")
            return False

    async def stop(self):
        """Stop the enhanced database manager"""
        self.running = False

        if self.cleanup_task:
            self.cleanup_task.cancel()

        if self.connection_pool.client:
            self.connection_pool.client.close()

        logger.debug("Enhanced database manager stopped")

    async def _initialize_collections(self):
        """Initialize collections with indexes for optimal performance"""
        try:
            db = self.connection_pool.db

            # Define collections
            self.collections = {
                "gangs": db["gangs"],
                "applications": db["applications"],
                "settings": db["settings"],
                "sticky_messages": db["sticky_messages"],
                "tebex_settings": db["tebex_settings"],
                "reaction_roles": db["reaction_roles"],
                "transactions": db["transactions"],
                "guild_settings": db["guild_settings"],
                "performance_logs": db["performance_logs"]
            }

            # Create optimized indexes for high-load scenarios
            indexes = [
                # Gang system indexes
                (self.collections["gangs"], [("guild_id", 1), ("roles.leader", 1)]),
                (self.collections["gangs"], [("invitations.target_id", 1)]),
                (self.collections["gangs"], [("invitations.created_at", 1)]),

                # Application system indexes
                (self.collections["applications"], [("guild_id", 1), ("status", 1)]),
                (self.collections["applications"], [("forms.user_id", 1)]),

                # Settings indexes
                (self.collections["settings"], [("guild_id", 1)]),
                (self.collections["guild_settings"], [("guild_id", 1)]),

                # Transaction indexes (skip transaction_id as it's handled by main database.py)
                (self.collections["transactions"], [("timestamp", -1)], "transactions_timestamp_idx"),
                (self.collections["transactions"], [("user_id", 1), ("timestamp", -1)], "transactions_user_timestamp_idx"),

                # Performance monitoring indexes
                (self.collections["performance_logs"], [("timestamp", -1)]),
                (self.collections["performance_logs"], [("metric_type", 1), ("timestamp", -1)])
            ]

            # Create indexes safely with explicit names
            for index_info in indexes:
                try:
                    if len(index_info) == 3:
                        collection, index_spec, index_name = index_info
                        await self._create_index_safely(collection, index_spec, index_name)
                    else:
                        collection, index_spec = index_info
                        # Generate a safe name for indexes without explicit names
                        field_names = "_".join([f[0] if isinstance(f, tuple) else str(f) for f in index_spec])
                        index_name = f"enhanced_{collection.name}_{field_names}_idx"
                        await self._create_index_safely(collection, index_spec, index_name)
                except Exception as e:
                    logger.warning(f"Failed to create index: {e}")
                    continue

            logger.debug("Database collections and indexes initialized")

        except Exception as e:
            logger.error(f"Error initializing collections: {e}")

    async def _create_index_safely(self, collection, index_spec, index_name):
        """Create a single index safely, avoiding conflicts"""
        try:
            # Check if index already exists
            existing_indexes = await collection.list_indexes().to_list(length=None)
            existing_index_names = [idx.get("name") for idx in existing_indexes]

            if index_name not in existing_index_names:
                # Index doesn't exist, create it
                await collection.create_index(index_spec, name=index_name)
                logger.debug(f"Created enhanced index {index_name}")
            else:
                logger.debug(f"Enhanced index {index_name} already exists")

        except Exception as e:
            # Handle conflicts by checking if a similar index exists
            if "existing index" in str(e).lower():
                logger.debug(f"Index conflict for {index_name}, skipping (likely handled by main database)")
            else:
                logger.warning(f"Error creating enhanced index {index_name}: {e}")

    async def _cleanup_loop(self):
        """Background cleanup loop"""
        while self.running:
            try:
                # Clean up expired cache entries
                expired_count = self.query_cache.cleanup_expired()
                if expired_count > 0:
                    logger.debug(f"Cleaned up {expired_count} expired cache entries")

                # Wait for next cleanup cycle
                await asyncio.sleep(300)  # 5 minutes

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")

    async def find_one_cached(self, collection_name: str, query: Dict,
                             projection: Dict = None, ttl: int = None) -> Optional[Dict]:
        """Find one document with caching"""
        try:
            # Check cache first
            cached_result = self.query_cache.get(collection_name, query, projection)
            if cached_result is not None:
                return cached_result

            # Query database
            collection = self.collections.get(collection_name)
            if collection is None:
                logger.warning(f"Collection {collection_name} not found")
                # Try to get the collection if database is available
                if self.connection_pool.db is not None:
                    try:
                        collection = self.connection_pool.db[collection_name]
                        self.collections[collection_name] = collection
                    except Exception as e:
                        logger.warning(f"Could not access collection {collection_name}: {e}")
                        return None
                else:
                    return None

            async def query_operation():
                try:
                    if projection:
                        return await collection.find_one(query, projection)
                    else:
                        return await collection.find_one(query)
                except Exception as e:
                    logger.warning(f"Query failed for collection {collection_name}: {e}")
                    return None

            result = await self.connection_pool.execute_query(query_operation)

            # Cache result (even if None to avoid repeated failed queries)
            self.query_cache.set(collection_name, query, result, projection, ttl)

            return result

        except Exception as e:
            logger.error(f"Error in find_one_cached for {collection_name}: {e}")
            return None

    async def find_many_cached(self, collection_name: str, query: Dict,
                              projection: Dict = None, limit: int = None,
                              sort: List = None, ttl: int = None) -> List[Dict]:
        """Find multiple documents with caching"""
        # For complex queries, we'll cache based on a simplified key
        cache_query = query.copy()
        if limit:
            cache_query['_limit'] = limit
        if sort:
            cache_query['_sort'] = sort

        # Check cache first
        cached_result = self.query_cache.get(collection_name, cache_query, projection)
        if cached_result is not None:
            return cached_result

        # Query database
        collection = self.collections.get(collection_name)
        if collection is None:
            raise ValueError(f"Collection {collection_name} not found")

        async def query_operation():
            cursor = collection.find(query, projection)
            if sort:
                cursor = cursor.sort(sort)
            if limit:
                cursor = cursor.limit(limit)
            return await cursor.to_list(length=limit)

        result = await self.connection_pool.execute_query(query_operation)

        # Cache result
        if result is not None:
            self.query_cache.set(collection_name, cache_query, result, projection, ttl)

        return result

    async def update_one_with_cache_invalidation(self, collection_name: str,
                                               filter_query: Dict, update: Dict,
                                               upsert: bool = False) -> bool:
        """Update document and invalidate related cache entries"""
        try:
            collection = self.collections.get(collection_name)
            if collection is None:
                logger.warning(f"Collection {collection_name} not found, attempting to create")
                # Try to get the collection (this will create it if it doesn't exist)
                if self.connection_pool.db is not None:
                    collection = self.connection_pool.db[collection_name]
                    self.collections[collection_name] = collection
                else:
                    logger.error("Database connection not available for collection creation")
                    return False

            async def update_operation():
                try:
                    result = await collection.update_one(filter_query, update, upsert=upsert)
                    return result.acknowledged
                except Exception as e:
                    logger.error(f"Update operation failed for {collection_name}: {e}")
                    return False

            success = await self.connection_pool.execute_query(update_operation)

            if success:
                # Invalidate cache for this collection
                self.query_cache.invalidate_collection(collection_name)

            return success

        except Exception as e:
            logger.error(f"Error in update_one_with_cache_invalidation for {collection_name}: {e}")
            return False

    async def insert_one_with_cache_invalidation(self, collection_name: str,
                                               document: Dict) -> Optional[str]:
        """Insert document and invalidate related cache entries"""
        collection = self.collections.get(collection_name)
        if collection is None:
            raise ValueError(f"Collection {collection_name} not found")

        async def insert_operation():
            result = await collection.insert_one(document)
            return str(result.inserted_id) if result.inserted_id else None

        inserted_id = await self.connection_pool.execute_query(insert_operation)

        if inserted_id:
            # Invalidate cache for this collection
            self.query_cache.invalidate_collection(collection_name)

        return inserted_id

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics"""
        return {
            'connection_pool': self.connection_pool.get_stats(),
            'query_cache': self.query_cache.get_stats(),
            'collections_count': len(self.collections),
            'is_running': self.running
        }

# Global enhanced database manager instance
enhanced_db_manager = EnhancedDatabaseManager()

def get_enhanced_db_manager():
    """Get the global enhanced database manager instance"""
    return enhanced_db_manager
