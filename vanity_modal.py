import discord
from discord.ui import Modal, TextInput
from database import save_guild_settings, get_guild_settings

class VanityNotificationModal(Modal):
    async def __init__(self, guild_id):
        super().__init__(title="Customize Vanity Notifications")
        
        # Get current guild settings
        settings = await get_guild_settings(guild_id)
        vanity_settings = settings.get("vanity", {}) if settings else {}
        
        self.add_role_title = TextInput(
            label="Add Role Title",
            placeholder="Enter title for when role is added",
            default=vanity_settings.get("add_role_title", "Priority Queue Granted"),
            required=True
        )
        
        self.add_role_message = TextInput(
            label="Add Role Message",
            placeholder="Use {member} for user mention and {status} for vanity status",
            style=discord.TextStyle.paragraph,
            default=vanity_settings.get("add_role_message", "{member} just added '{status}' as their custom Discord status and received free priority queue!"),
            required=True
        )
        
        self.remove_role_title = TextInput(
            label="Remove Role Title",
            placeholder="Enter title for when role is removed",
            default=vanity_settings.get("remove_role_title", "Priority Queue Removed"),
            required=True
        )
        
        self.remove_role_message = TextInput(
            label="Remove Role Message",
            placeholder="Use {member} for user mention and {status} for vanity status",
            style=discord.TextStyle.paragraph,
            default=vanity_settings.get("remove_role_message", "{member} has removed '{status}' from their custom Discord status"),
            required=True
        )
        
        self.add_item(self.add_role_title)
        self.add_item(self.add_role_message)
        self.add_item(self.remove_role_title)
        self.add_item(self.remove_role_message)
    
    async def on_submit(self, interaction: discord.Interaction):
        try:
            # Get current guild settings
            settings = await get_guild_settings(interaction.guild_id)
            if not settings:
                settings = {
                    "guild_id": interaction.guild_id,
                    "vanity": {},
                    "notification_channel_id": None
                }
            
            # Update notification settings
            settings["vanity"].update({
                "add_role_title": self.add_role_title.value,
                "add_role_message": self.add_role_message.value,
                "remove_role_title": self.remove_role_title.value,
                "remove_role_message": self.remove_role_message.value
            })
            
            # Save updated settings
            save_guild_settings(interaction.guild_id, settings)
            
            await interaction.response.send_message(
                "✅ Notification messages have been customized successfully!",
                ephemeral=True
            )
            
        except Exception as e:
            await interaction.response.send_message(
                f"❌ An error occurred while saving notification settings: {str(e)}",
                ephemeral=True
            )