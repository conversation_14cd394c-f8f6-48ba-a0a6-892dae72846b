"""
Panel Customization Manager for Discord Ticket Panel Redesign

This module provides comprehensive customization management for the modern ticket panel system,
including validation, persistence, and configuration management.
"""

import discord
import re
import aiohttp
import asyncio
from typing import Dict, Any, Optional, Tuple, List, Union
from datetime import datetime, timezone
import logging
from urllib.parse import urlparse
import json

# Configure logging
logger = logging.getLogger('panel_customization')

class PanelCustomizationManager:
    """
    Comprehensive customization engine for ticket panel appearance and content.
    
    Provides validation, persistence, and management of all customization options
    including text content, colors, images, and layout preferences.
    """
    
    def __init__(self, ticket_config_collection, save_callback):
        """
        Initialize the customization manager.
        
        Args:
            ticket_config_collection: MongoDB collection for ticket configuration
            save_callback: Async function to save ticket data
        """
        self.ticket_config_collection = ticket_config_collection
        self.save_callback = save_callback
        
        # Discord embed limits
        self.EMBED_TITLE_LIMIT = 256
        self.EMBED_DESCRIPTION_LIMIT = 4096
        self.EMBED_FOOTER_LIMIT = 2048
        self.EMBED_FIELD_NAME_LIMIT = 256
        self.EMBED_FIELD_VALUE_LIMIT = 1024
        self.EMBED_TOTAL_LIMIT = 6000
        
        # Image validation settings
        self.SUPPORTED_IMAGE_FORMATS = ['.png', '.jpg', '.jpeg', '.gif', '.webp']
        self.MAX_IMAGE_SIZE_MB = 8
        self.RECOMMENDED_IMAGE_WIDTH = 400
        self.RECOMMENDED_IMAGE_HEIGHT = 200
        
        # Default customization configuration
        self.default_config = {
            "embed": {
                "title": "🎫 Support Tickets",
                "description": "Need help? Create a support ticket and our team will assist you promptly.",
                "color": 0x2b2d31,  # Discord dark theme color
                "footer_text": "Click a button below to create a ticket",
                "image_url": None
            },
            "layout": {
                "button_style": "secondary",  # primary, secondary, success, danger
                "show_categories": True,
                "compact_mode": False
            },
            "branding": {
                "server_name": None,  # Auto-detected from guild
                "support_team": "Support Team",
                "contact_info": None
            },
            "advanced": {
                "auto_close_inactive": False,
                "inactive_timeout_hours": 24,
                "require_reason": False,
                "allow_user_close": True
            }
        }
    
    def get_customization_config(self, guild_id: int) -> Dict[str, Any]:
        """
        Get the current customization configuration for a guild.
        
        Args:
            guild_id: Discord guild ID
            
        Returns:
            Dictionary containing the customization configuration
        """
        try:
            # Import ticket_config from the main module
            from tickets import ticket_config
            
            # Get existing customization or create default
            customization = ticket_config.get("panel_customization", {})
            
            # Merge with defaults to ensure all keys exist
            merged_config = self._deep_merge_config(self.default_config.copy(), customization)
            
            # Auto-detect server name if not set
            if not merged_config["branding"]["server_name"]:
                try:
                    guild = discord.utils.get(discord.Client().guilds, id=guild_id)
                    if guild:
                        merged_config["branding"]["server_name"] = guild.name
                except:
                    merged_config["branding"]["server_name"] = "Server"
            
            return merged_config
            
        except Exception as e:
            logger.error(f"Error getting customization config for guild {guild_id}: {e}")
            return self.default_config.copy()
    
    async def update_customization(self, guild_id: int, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Update the customization configuration for a guild.
        
        Args:
            guild_id: Discord guild ID
            config: New customization configuration
            
        Returns:
            Tuple of (success: bool, errors: List[str])
        """
        try:
            # Validate the configuration
            is_valid, validation_errors = await self.validate_customization(config)
            
            if not is_valid:
                return False, validation_errors
            
            # Import ticket_config from the main module
            from tickets import ticket_config
            
            # Sanitize and prepare the configuration
            sanitized_config = self._sanitize_config(config)
            
            # Update the ticket configuration
            if "panel_customization" not in ticket_config:
                ticket_config["panel_customization"] = {}
            
            ticket_config["panel_customization"] = sanitized_config
            
            # Save to database
            await self.save_callback()
            
            logger.info(f"Updated customization config for guild {guild_id}")
            return True, []
            
        except Exception as e:
            error_msg = f"Error updating customization config: {str(e)}"
            logger.error(error_msg)
            return False, [error_msg]
    
    async def validate_customization(self, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate a customization configuration.
        
        Args:
            config: Configuration to validate
            
        Returns:
            Tuple of (is_valid: bool, errors: List[str])
        """
        errors = []
        
        try:
            # Validate embed section
            if "embed" in config:
                embed_errors = await self._validate_embed_config(config["embed"])
                errors.extend(embed_errors)
            
            # Validate layout section
            if "layout" in config:
                layout_errors = self._validate_layout_config(config["layout"])
                errors.extend(layout_errors)
            
            # Validate branding section
            if "branding" in config:
                branding_errors = self._validate_branding_config(config["branding"])
                errors.extend(branding_errors)
            
            # Validate advanced section
            if "advanced" in config:
                advanced_errors = self._validate_advanced_config(config["advanced"])
                errors.extend(advanced_errors)
            
            # Check total embed size
            if "embed" in config:
                total_size_error = self._validate_total_embed_size(config["embed"])
                if total_size_error:
                    errors.append(total_size_error)
            
            return len(errors) == 0, errors
            
        except Exception as e:
            logger.error(f"Error during validation: {e}")
            return False, [f"Validation error: {str(e)}"]
    
    async def _validate_embed_config(self, embed_config: Dict[str, Any]) -> List[str]:
        """Validate embed configuration section."""
        errors = []
        
        # Validate title
        if "title" in embed_config:
            title = embed_config["title"]
            if title and len(str(title)) > self.EMBED_TITLE_LIMIT:
                errors.append(f"Title exceeds {self.EMBED_TITLE_LIMIT} character limit")
        
        # Validate description
        if "description" in embed_config:
            description = embed_config["description"]
            if description and len(str(description)) > self.EMBED_DESCRIPTION_LIMIT:
                errors.append(f"Description exceeds {self.EMBED_DESCRIPTION_LIMIT} character limit")
        
        # Validate footer text
        if "footer_text" in embed_config:
            footer = embed_config["footer_text"]
            if footer and len(str(footer)) > self.EMBED_FOOTER_LIMIT:
                errors.append(f"Footer text exceeds {self.EMBED_FOOTER_LIMIT} character limit")
        
        # Validate color
        if "color" in embed_config:
            color = embed_config["color"]
            if color is not None:
                if not isinstance(color, int) or color < 0 or color > 0xFFFFFF:
                    errors.append("Color must be a valid hex integer (0x000000 to 0xFFFFFF)")
        
        # Validate image URL
        if "image_url" in embed_config:
            image_url = embed_config["image_url"]
            if image_url:
                image_errors = await self._validate_image_url(image_url)
                errors.extend(image_errors)
        
        return errors
    
    def _validate_layout_config(self, layout_config: Dict[str, Any]) -> List[str]:
        """Validate layout configuration section."""
        errors = []
        
        # Validate button style
        if "button_style" in layout_config:
            style = layout_config["button_style"]
            valid_styles = ["primary", "secondary", "success", "danger"]
            if style not in valid_styles:
                errors.append(f"Button style must be one of: {', '.join(valid_styles)}")
        
        # Validate boolean fields
        boolean_fields = ["show_categories", "compact_mode"]
        for field in boolean_fields:
            if field in layout_config:
                if not isinstance(layout_config[field], bool):
                    errors.append(f"{field} must be a boolean value")
        
        return errors
    
    def _validate_branding_config(self, branding_config: Dict[str, Any]) -> List[str]:
        """Validate branding configuration section."""
        errors = []
        
        # Validate text fields
        text_fields = {
            "server_name": 100,
            "support_team": 100,
            "contact_info": 200
        }
        
        for field, max_length in text_fields.items():
            if field in branding_config:
                value = branding_config[field]
                if value and len(str(value)) > max_length:
                    errors.append(f"{field} exceeds {max_length} character limit")
        
        return errors
    
    def _validate_advanced_config(self, advanced_config: Dict[str, Any]) -> List[str]:
        """Validate advanced configuration section."""
        errors = []
        
        # Validate boolean fields
        boolean_fields = ["auto_close_inactive", "require_reason", "allow_user_close"]
        for field in boolean_fields:
            if field in advanced_config:
                if not isinstance(advanced_config[field], bool):
                    errors.append(f"{field} must be a boolean value")
        
        # Validate timeout hours
        if "inactive_timeout_hours" in advanced_config:
            timeout = advanced_config["inactive_timeout_hours"]
            if not isinstance(timeout, (int, float)) or timeout < 1 or timeout > 168:  # 1 hour to 1 week
                errors.append("Inactive timeout must be between 1 and 168 hours")
        
        return errors
    
    async def _validate_image_url(self, image_url: str) -> List[str]:
        """
        Validate image URL for accessibility and format.
        
        Args:
            image_url: URL to validate
            
        Returns:
            List of validation errors
        """
        errors = []
        warnings = []
        
        try:
            # Basic URL validation
            parsed = urlparse(image_url)
            if not parsed.scheme or not parsed.netloc:
                errors.append("Invalid image URL format")
                return errors
            
            # Check if URL is accessible
            timeout = aiohttp.ClientTimeout(total=10)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                try:
                    async with session.head(image_url) as response:
                        # Check if URL is accessible
                        if response.status >= 400:
                            errors.append(f"Image URL not accessible (HTTP {response.status})")
                            return errors
                        
                        # Check content type
                        content_type = response.headers.get('content-type', '').lower()
                        if not content_type.startswith('image/'):
                            errors.append("URL does not point to an image")
                            return errors
                        
                        # Check file size
                        content_length = response.headers.get('content-length')
                        if content_length:
                            size_mb = int(content_length) / (1024 * 1024)
                            if size_mb > self.MAX_IMAGE_SIZE_MB:
                                errors.append(f"Image size ({size_mb:.1f}MB) exceeds {self.MAX_IMAGE_SIZE_MB}MB limit")
                        
                        # Check file extension
                        path = parsed.path.lower()
                        if not any(path.endswith(ext) for ext in self.SUPPORTED_IMAGE_FORMATS):
                            warnings.append(f"Image format may not be supported. Recommended: {', '.join(self.SUPPORTED_IMAGE_FORMATS)}")
                
                except asyncio.TimeoutError:
                    errors.append("Image URL request timed out")
                except aiohttp.ClientError as e:
                    errors.append(f"Error accessing image URL: {str(e)}")
        
        except Exception as e:
            errors.append(f"Error validating image URL: {str(e)}")
        
        # Add warnings as informational errors (they don't prevent saving)
        for warning in warnings:
            logger.warning(f"Image validation warning: {warning}")
        
        return errors
    
    def _validate_total_embed_size(self, embed_config: Dict[str, Any]) -> Optional[str]:
        """
        Validate that the total embed size doesn't exceed Discord limits.
        
        Args:
            embed_config: Embed configuration to check
            
        Returns:
            Error message if size exceeds limit, None otherwise
        """
        try:
            total_chars = 0
            
            # Count characters in each field
            if embed_config.get("title"):
                total_chars += len(str(embed_config["title"]))
            
            if embed_config.get("description"):
                total_chars += len(str(embed_config["description"]))
            
            if embed_config.get("footer_text"):
                total_chars += len(str(embed_config["footer_text"]))
            
            # Add estimated characters for fields that will be added by the system
            total_chars += 200  # Buffer for system-added content
            
            if total_chars > self.EMBED_TOTAL_LIMIT:
                return f"Total embed content ({total_chars} chars) exceeds Discord limit ({self.EMBED_TOTAL_LIMIT} chars)"
            
            return None
            
        except Exception as e:
            logger.error(f"Error validating total embed size: {e}")
            return "Error calculating embed size"
    
    def _sanitize_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize configuration by removing invalid values and trimming text.
        
        Args:
            config: Configuration to sanitize
            
        Returns:
            Sanitized configuration
        """
        sanitized = {}
        
        try:
            # Sanitize embed section
            if "embed" in config:
                sanitized["embed"] = {}
                embed = config["embed"]
                
                # Sanitize text fields
                text_fields = {
                    "title": self.EMBED_TITLE_LIMIT,
                    "description": self.EMBED_DESCRIPTION_LIMIT,
                    "footer_text": self.EMBED_FOOTER_LIMIT
                }
                
                for field, limit in text_fields.items():
                    if field in embed and embed[field]:
                        value = str(embed[field]).strip()
                        if len(value) > limit:
                            value = value[:limit-3] + "..."
                        sanitized["embed"][field] = value
                    else:
                        sanitized["embed"][field] = embed.get(field)
                
                # Sanitize color
                if "color" in embed:
                    color = embed["color"]
                    if isinstance(color, int) and 0 <= color <= 0xFFFFFF:
                        sanitized["embed"]["color"] = color
                    else:
                        sanitized["embed"]["color"] = self.default_config["embed"]["color"]
                
                # Sanitize image URL
                if "image_url" in embed:
                    url = embed["image_url"]
                    if url and isinstance(url, str) and url.strip():
                        sanitized["embed"]["image_url"] = url.strip()
                    else:
                        sanitized["embed"]["image_url"] = None
            
            # Sanitize layout section
            if "layout" in config:
                sanitized["layout"] = {}
                layout = config["layout"]
                
                # Button style
                style = layout.get("button_style", "secondary")
                valid_styles = ["primary", "secondary", "success", "danger"]
                sanitized["layout"]["button_style"] = style if style in valid_styles else "secondary"
                
                # Boolean fields
                sanitized["layout"]["show_categories"] = bool(layout.get("show_categories", True))
                sanitized["layout"]["compact_mode"] = bool(layout.get("compact_mode", False))
            
            # Sanitize branding section
            if "branding" in config:
                sanitized["branding"] = {}
                branding = config["branding"]
                
                text_fields = {
                    "server_name": 100,
                    "support_team": 100,
                    "contact_info": 200
                }
                
                for field, limit in text_fields.items():
                    value = branding.get(field)
                    if value:
                        value = str(value).strip()
                        if len(value) > limit:
                            value = value[:limit-3] + "..."
                        sanitized["branding"][field] = value
                    else:
                        sanitized["branding"][field] = None
            
            # Sanitize advanced section
            if "advanced" in config:
                sanitized["advanced"] = {}
                advanced = config["advanced"]
                
                # Boolean fields
                boolean_fields = ["auto_close_inactive", "require_reason", "allow_user_close"]
                for field in boolean_fields:
                    sanitized["advanced"][field] = bool(advanced.get(field, self.default_config["advanced"][field]))
                
                # Timeout hours
                timeout = advanced.get("inactive_timeout_hours", 24)
                if isinstance(timeout, (int, float)) and 1 <= timeout <= 168:
                    sanitized["advanced"]["inactive_timeout_hours"] = int(timeout)
                else:
                    sanitized["advanced"]["inactive_timeout_hours"] = 24
            
            return sanitized
            
        except Exception as e:
            logger.error(f"Error sanitizing config: {e}")
            return self.default_config.copy()
    
    def _deep_merge_config(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """
        Deep merge two configuration dictionaries.
        
        Args:
            base: Base configuration
            override: Configuration to merge in
            
        Returns:
            Merged configuration
        """
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def get_image_recommendations(self) -> Dict[str, Any]:
        """
        Get image format and size recommendations.
        
        Returns:
            Dictionary with image recommendations
        """
        return {
            "supported_formats": self.SUPPORTED_IMAGE_FORMATS,
            "max_size_mb": self.MAX_IMAGE_SIZE_MB,
            "recommended_dimensions": {
                "width": self.RECOMMENDED_IMAGE_WIDTH,
                "height": self.RECOMMENDED_IMAGE_HEIGHT
            },
            "tips": [
                "Use PNG or JPEG for best compatibility",
                "Keep file size under 2MB for faster loading",
                "Use 16:9 or 2:1 aspect ratio for best appearance",
                "Ensure image is accessible via HTTPS",
                "Test image URL before saving"
            ]
        }
    
    def get_color_presets(self) -> Dict[str, int]:
        """
        Get predefined color presets for common themes.
        
        Returns:
            Dictionary of color name to hex value
        """
        return {
            "Discord Dark": 0x2b2d31,
            "Discord Blurple": 0x5865F2,
            "Professional Black": 0x000000,
            "Dark Gray": 0x36393f,
            "Light Gray": 0x99aab5,
            "Success Green": 0x57f287,
            "Warning Yellow": 0xfee75c,
            "Error Red": 0xed4245,
            "Info Blue": 0x3498db,
            "Purple": 0x9b59b6,
            "Orange": 0xe67e22,
            "Teal": 0x1abc9c
        }
    
    async def reset_to_defaults(self, guild_id: int) -> Tuple[bool, List[str]]:
        """
        Reset customization to default values.
        
        Args:
            guild_id: Discord guild ID
            
        Returns:
            Tuple of (success: bool, errors: List[str])
        """
        try:
            # Import ticket_config from the main module
            from tickets import ticket_config
            
            # Reset to default configuration
            ticket_config["panel_customization"] = self.default_config.copy()
            
            # Save to database
            await self.save_callback()
            
            logger.info(f"Reset customization to defaults for guild {guild_id}")
            return True, []
            
        except Exception as e:
            error_msg = f"Error resetting customization: {str(e)}"
            logger.error(error_msg)
            return False, [error_msg]
    
    def export_customization(self, guild_id: int) -> Optional[str]:
        """
        Export customization configuration as JSON string.
        
        Args:
            guild_id: Discord guild ID
            
        Returns:
            JSON string of configuration or None if error
        """
        try:
            config = self.get_customization_config(guild_id)
            return json.dumps(config, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error exporting customization: {e}")
            return None
    
    async def import_customization(self, guild_id: int, json_config: str) -> Tuple[bool, List[str]]:
        """
        Import customization configuration from JSON string.
        
        Args:
            guild_id: Discord guild ID
            json_config: JSON string containing configuration
            
        Returns:
            Tuple of (success: bool, errors: List[str])
        """
        try:
            # Parse JSON
            config = json.loads(json_config)
            
            # Validate and update
            return await self.update_customization(guild_id, config)
            
        except json.JSONDecodeError as e:
            return False, [f"Invalid JSON format: {str(e)}"]
        except Exception as e:
            return False, [f"Error importing customization: {str(e)}"]