#!/usr/bin/env python3
"""
Test script to verify the webhook processing flow works correctly.
This script tests the process_payment_notification function directly.
"""

import asyncio
import sys
import os
from datetime import datetime, timezone

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the bot module
import bot
from bot import process_payment_notification

class MockMessage:
    """Mock Discord message object for testing"""
    def __init__(self, content, channel_id):
        self.content = content
        self.channel = MockChannel(channel_id)
        self.guild = MockGuild()
        self.webhook_id = 1378086188262490142  # Simulate webhook message
    
    async def delete(self):
        """Mock delete method"""
        print("✓ Mock webhook message deleted")

class MockChannel:
    """Mock Discord channel object"""
    def __init__(self, channel_id):
        self.id = channel_id
        self.name = "tebex-notifications"
    
    async def send(self, embed=None, view=None):
        """Mock send method"""
        print(f"✓ Message sent to channel {self.id}")
        if embed:
            print(f"  - Embed title: {embed.title}")
            print(f"  - Embed color: {hex(embed.color.value) if embed.color else 'None'}")
            print(f"  - Embed description: {embed.description[:100]}...")
        if view:
            print(f"  - View attached: {type(view).__name__}")
            print(f"  - View buttons: {len(view.children)} buttons")
            for i, child in enumerate(view.children):
                if hasattr(child, 'label'):
                    print(f"    Button {i+1}: {child.label} (custom_id: {child.custom_id})")
        return MockSentMessage()

class MockSentMessage:
    """Mock sent message object"""
    def __init__(self):
        self.id = 123456789  # Mock message ID

class MockGuild:
    """Mock Discord guild object"""
    def __init__(self):
        self.icon = None

async def test_webhook_processing():
    """Test the webhook processing flow"""
    print("🔄 Testing webhook processing flow...")
    
    # Test webhook content (same format as test_webhook.py)
    test_content = "Crimson has received a payment ╽ From: TestUser123 ╽ Price: $25.00 ╽ Package: VIP Package ╽ Transaction ID: TXN_20250804_TEST123 ╽ Email: <EMAIL>"
    
    # Create mock message
    mock_message = MockMessage(test_content, bot.tebex_channel)
    
    try:
        # Test the process_payment_notification function directly
        await process_payment_notification(mock_message, test_content)
        print("✅ Webhook processing test completed successfully!")
        
    except Exception as e:
        print(f"❌ Webhook processing test failed: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function"""
    print("🚀 Starting webhook processing test...")
    print(f"📍 Tebex channel ID: {bot.tebex_channel}")

    # Set a mock tebex channel ID for testing
    bot.tebex_channel = 123456789  # Mock channel ID
    print(f"📍 Using mock Tebex channel ID: {bot.tebex_channel}")

    # Initialize database connection
    try:
        from database import init_db
        await init_db()
        print("✓ Database connection initialized")
    except Exception as e:
        print(f"⚠ Database initialization failed: {e}")

    # Run the webhook processing test
    await test_webhook_processing()

    print("🏁 Test completed!")

if __name__ == "__main__":
    # Run the test
    asyncio.run(main())
