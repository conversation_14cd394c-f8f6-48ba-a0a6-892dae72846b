import discord
from discord.ui import <PERSON><PERSON>, <PERSON>, Modal, TextInput
import discord.ui
import asyncio
import json
import os
import traceback
from datetime import datetime
from collections import defaultdict, deque
import asyncio
import time
import random



from bot_instance import bot

class TicketRateLimitHandler:
	def __init__(self):
		self.rate_limits = {}
		self.queues = {}
		self.processing = {}
		self.max_retries = 15  # Increased retry limit
		self.base_delay = 1.0  # Start with 1 second delay
		self.max_delay = 600.0  # Maximum delay of 10 minutes
		self.bulk_queue = asyncio.Queue()
		self.bulk_processing = False
		self.batch_sizes = defaultdict(lambda: 10)  # Default batch size of 10
		self.success_threshold = 5  # Number of successful operations before increasing batch size
		self.success_counts = defaultdict(int)

	async def execute(self, key, coroutine, *args, **kwargs):
		"""Execute a coroutine with enhanced rate limit handling"""
		if key not in self.queues:
			self.queues[key] = asyncio.Queue()
			self.processing[key] = False
			
		await self.queues[key].put((coroutine, args, kwargs))
		
		if not self.processing[key]:
			self.processing[key] = True
			asyncio.create_task(self._process_queue(key))

	async def execute_bulk(self, key, operations):
		"""Handle bulk operations with dynamic batch sizing"""
		for op in operations:
			await self.bulk_queue.put((key, op))
			
		if not self.bulk_processing:
			self.bulk_processing = True
			asyncio.create_task(self._process_bulk_queue())

	async def _process_bulk_queue(self):
		"""Process bulk operations with smart batching"""
		try:
			while not self.bulk_queue.empty():
				key = None
				batch = []
				batch_size = self.batch_sizes[key]
				
				# Gather batch of operations
				while len(batch) < batch_size and not self.bulk_queue.empty():
					key, op = await self.bulk_queue.get()
					batch.append(op)

				if batch:
					try:
						# Execute batch with retry logic
						success = await self._execute_with_backoff(key, batch)
						
						# Adjust batch size based on success/failure
						if success:
							self.success_counts[key] += 1
							if self.success_counts[key] >= self.success_threshold:
								self.batch_sizes[key] = min(50, self.batch_sizes[key] + 5)
								self.success_counts[key] = 0
						else:
							self.batch_sizes[key] = max(1, self.batch_sizes[key] // 2)
							self.success_counts[key] = 0
							
					except Exception as e:
						print(f"Error in bulk processing: {e}")
						self.batch_sizes[key] = max(1, self.batch_sizes[key] // 2)
						
					# Add delay between batches with jitter
					jitter = random.uniform(0, 0.1)
					await asyncio.sleep(1.0 + jitter)
					
		finally:
			self.bulk_processing = False

	async def _process_queue(self, key):
		"""Process queued items with enhanced rate limiting"""
		try:
			while not self.queues[key].empty():
				if key in self.rate_limits:
					wait_time = self.rate_limits[key] - time.time()
					if wait_time > 0:
						jitter = random.uniform(0, 0.1 * wait_time)
						await asyncio.sleep(wait_time + jitter)
						
				coroutine, args, kwargs = await self.queues[key].get()
				
				success = False
				for attempt in range(self.max_retries):
					try:
						await coroutine(*args, **kwargs)
						success = True
						break
					except discord.HTTPException as e:
						if e.status == 429:  # Rate limit
							retry_after = e.retry_after if hasattr(e, 'retry_after') else None
							if retry_after is None:
								retry_after = min(self.max_delay, self.base_delay * (2 ** attempt))
							
							jitter = random.uniform(0, 0.1 * retry_after)
							total_delay = retry_after + jitter
							
							self.rate_limits[key] = time.time() + total_delay
							print(f"Rate limited on {key}, waiting {total_delay:.2f}s (Attempt {attempt + 1}/{self.max_retries})")
							await asyncio.sleep(total_delay)
							continue
						raise
					except Exception as e:
						print(f"Error in operation: {e}")
						if attempt == self.max_retries - 1:
							raise
						
						backoff = min(self.max_delay, self.base_delay * (2 ** attempt))
						await asyncio.sleep(backoff)
						
				if success:
					# Add successful operation delay with jitter
					jitter = random.uniform(0, 0.1)
					await asyncio.sleep(0.5 + jitter)
					
		finally:
			self.processing[key] = False

	async def _execute_with_backoff(self, key, operations):
		"""Execute operations with exponential backoff"""
		for attempt in range(self.max_retries):
			try:
				for op in operations:
					await op()
				return True
			except discord.HTTPException as e:
				if e.status == 429:
					retry_after = e.retry_after if hasattr(e, 'retry_after') else None
					if retry_after is None:
						retry_after = min(self.max_delay, self.base_delay * (2 ** attempt))
						
					jitter = random.uniform(0, 0.1 * retry_after)
					total_delay = retry_after + jitter
					
					print(f"Rate limited in bulk operation, waiting {total_delay:.2f}s")
					await asyncio.sleep(total_delay)
					continue
				raise
		return False

# Create global instance
ticket_rate_limiter = TicketRateLimitHandler()

class CloseConfirmView(discord.ui.View):
	def __init__(self):
		super().__init__(timeout=60)  # 60 second timeout
		self.value = None

	@discord.ui.button(label="Close", style=discord.ButtonStyle.red)
	async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
		await interaction.response.defer(ephemeral=True)
		self.value = True
		self.stop()

	@discord.ui.button(label="Cancel", style=discord.ButtonStyle.grey)
	async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
		await interaction.response.defer(ephemeral=True)
		self.value = False
		self.stop()

async def modal_callback(m_i: discord.Interaction):
	try:
		channel_id = int(m_i.data["components"][0]["components"][0]["value"])
		channel = m_i.guild.get_channel(channel_id)
		if not channel:
			await m_i.response.send_message("Channel not found!", ephemeral=True)
			return
		
		success = await set_ticket_channel(channel_id)
		if success:
			try:
				await m_i.response.send_message("Ticket channel set and panel created successfully!", ephemeral=True)
			except discord.errors.NotFound:
				await m_i.followup.send("Ticket channel set and panel created successfully!", ephemeral=True)
		else:
			try:
				await m_i.response.send_message("Failed to set ticket channel", ephemeral=True)
			except discord.errors.NotFound:
				await m_i.followup.send("Failed to set ticket channel", ephemeral=True)
	except ValueError:
		try:
			await m_i.response.send_message("Invalid channel ID", ephemeral=True)
		except discord.errors.NotFound:
			await m_i.followup.send("Invalid channel ID", ephemeral=True)
from discord.ext import commands
from bot_instance import bot
from discord.ui import Button, View, Select
import asyncio
import json
import os
import io
import traceback  # Add this import
from datetime import datetime

class TicketModal(discord.ui.Modal):
	def __init__(self, category_id, ticket_number):
		super().__init__(title="Support Ticket")
		self.category_id = category_id
		self.ticket_number = ticket_number
		
		self.add_item(discord.ui.TextInput(
			label="TELL US MORE ABOUT YOUR PROBLEM.",
			placeholder="Describe your problem here",
			style=discord.TextStyle.paragraph,
			required=True
		))
		
		self.add_item(discord.ui.TextInput(
			label="DO YOU HAVE ANY EVIDANCE? (IF ANY?)",
			placeholder="Provide any evidence if applicable",
			style=discord.TextStyle.paragraph,
			required=False
		))
		
		self.add_item(discord.ui.TextInput(
			label="WHAT KIND OF SUPPORT YOU LOOKING FOR?",
			placeholder="Describe the type of support needed",
			style=discord.TextStyle.paragraph,
			required=True
		))

	async def on_submit(self, interaction: discord.Interaction):
		try:
			problem = self.children[0].value
			evidence = self.children[1].value
			support_type = self.children[2].value
			
			# Defer the response immediately to prevent timeout
			await interaction.response.defer(ephemeral=True)
			
			guild = interaction.guild
			category = guild.get_channel(int(self.category_id))
			
			if not category:
				await interaction.followup.send(
					"Invalid ticket category! Please contact an administrator.",
					ephemeral=True
				)
				return

			channel_name = f"ticket-{self.ticket_number:04d}"
			
			channel = await guild.create_text_channel(
				channel_name,
				category=category,
				topic=f"Ticket #{self.ticket_number:04d} | {interaction.user.name}"
			)
			
			# Set permissions
			await channel.set_permissions(interaction.user, read_messages=True, send_messages=True)
			await channel.set_permissions(guild.default_role, read_messages=False)
			
			for role_id in ticket_config.get("staff_roles", []):
				role = guild.get_role(role_id)
				if role:
					await channel.set_permissions(role, read_messages=True, send_messages=True)
			
			# Create welcome embed
			welcome_embed = discord.Embed(
				description=f"{interaction.user.mention} Welcome\nSupport will be with you shortly.\nTo close this ticket react with 🔒",
				color=0x2b2d31
			)
			
			# Create questions embed without ticket number in title
			questions_embed = discord.Embed(
				color=0x2b2d31  # Keep just the dark theme color
			)

			
			questions_embed.add_field(
				name="TELL US MORE ABOUT YOUR PROBLEM.",
				value=f"\n{problem}\n\u200b",
				inline=False
			)
			
			questions_embed.add_field(
				name="DO YOU HAVE ANY EVIDANCE? (IF ANY?)",
				value=f"\n{evidence if evidence else 'no'}\n\u200b",
				inline=False
			)
			
			questions_embed.add_field(
				name="WHAT KIND OF SUPPORT YOU LOOKING FOR?",
				value=f"\n{support_type if support_type else 'no'}\n\u200b",
				inline=False
			)
			

			
			# Create view with themed close buttons
			view = discord.ui.View()
			
			close_button = discord.ui.Button(
				label="Close",
				style=discord.ButtonStyle.grey,
				custom_id="close_ticket",
				emoji="🔒"
			)
			
			close_reason_button = discord.ui.Button(
				label="Close with Reason",
				style=discord.ButtonStyle.grey,
				custom_id="close_with_reason",
				emoji="📝"
			)
			
			view.add_item(close_button)
			view.add_item(close_reason_button)
			
			# Send welcome message as plain text
			welcome_message = f"{interaction.user.mention} Welcome"
			await channel.send(welcome_message)
			
			# Send support info in an embed
			support_embed = discord.Embed(
				description="Support will be with you shortly.\nTo close this ticket react with 🔒",
				color=0x2b2d31
			)
			await channel.send(embed=support_embed)
			
			# Send questions embed with buttons
			message = await channel.send(embed=questions_embed, view=view)
			
			# Store ticket info
			active_tickets[str(channel.id)] = {
				"user_id": interaction.user.id,
				"ticket_number": self.ticket_number,
				"category_id": self.category_id,
				"message_id": message.id
			}
			
			await save_ticket_data()
			
			await interaction.followup.send(
				f"Ticket created! Check {channel.mention}",
				ephemeral=True
			)
			
		except Exception as e:
			print(f"Error in modal submit: {e}")
			import traceback
			traceback.print_exc()
			
			try:
				await interaction.followup.send(
					"An error occurred while creating your ticket.",
					ephemeral=True
				)
			except:
				print("Could not send error message")




import io
import random
from datetime import datetime

async def channel_operation_with_backoff(operation, max_retries=5):
	"""Execute channel operations with exponential backoff for rate limits"""
	base_delay = 1.0  # Start with 1 second delay
	max_delay = 600.0  # Maximum delay of 10 minutes
	
	for attempt in range(max_retries):
		try:
			return await operation()
		except discord.HTTPException as e:
			if e.status == 429:  # Rate limit error
				# Get retry_after from the error if available, otherwise calculate it
				retry_after = e.retry_after if hasattr(e, 'retry_after') else min(base_delay * (2 ** attempt), max_delay)
				
				# Add jitter to prevent thundering herd
				jitter = random.uniform(0, 0.1 * retry_after)
				total_delay = retry_after + jitter
				
				print(f"Rate limited on channel operation, waiting {total_delay:.2f}s (Attempt {attempt + 1}/{max_retries})")
				await asyncio.sleep(total_delay)
				continue
			raise  # Re-raise other HTTP exceptions
	
	raise Exception(f"Failed channel operation after {max_retries} attempts")

async def delete_message_with_backoff(message, max_retries=5):
	"""Delete a message with improved exponential backoff for rate limits"""
	base_delay = 0.1  # Start with 100ms delay
	max_delay = 5.0   # Maximum delay of 5 seconds
	
	for attempt in range(max_retries):
		try:
			await message.delete()
			return True
		except discord.HTTPException as e:
			if e.status == 429:  # Rate limit error
				# Get retry_after from the error if available, otherwise calculate it
				retry_after = e.retry_after if hasattr(e, 'retry_after') else min(base_delay * (2 ** attempt), max_delay)
				
				# Add jitter to prevent thundering herd
				jitter = random.uniform(0, 0.1 * retry_after)
				total_delay = retry_after + jitter
				
				print(f"Rate limited on delete, waiting {total_delay:.2f}s (Attempt {attempt + 1}/{max_retries})")
				await asyncio.sleep(total_delay)
				continue
			elif e.status == 404:  # Message already deleted
				return True
			else:
				print(f"Error deleting message: {e}")
				return False
		except Exception as e:
			print(f"Unexpected error deleting message: {e}")
			return False
	
	print(f"Failed to delete message after {max_retries} attempts")
	return False

import random
import time




async def perform_channel_operation(operation):
	"""Simple channel operation without rate limit handling"""
	try:
		return await operation()
	except Exception as e:
		print(f"Error in channel operation: {e}")
		return None







# Initialize ticket configuration with default structure
ticket_config = {
	"categories": {},
	"staff_roles": [],
	"transcript_channel": None,
	"ticket_channel": None,
	"last_ticket_number": 0  # Add this to track the last used number
}

# Active tickets storage
active_tickets = {}
ticket_counter = 0


class CategoryModal(discord.ui.Modal):
	def __init__(self):
		super().__init__(title="Add Ticket Category")
		self.add_item(discord.ui.TextInput(
			label="Category Name",
			placeholder="e.g., Support Tickets",
			required=True
		))
		self.add_item(discord.ui.TextInput(
			label="Category Description",
			placeholder="Description for this ticket category",
			style=discord.TextStyle.paragraph,
			required=True
		))

	async def on_submit(self, interaction: discord.Interaction):
		name = self.children[0].value
		description = self.children[1].value
		
		# Create category in Discord
		try:
			category = await interaction.guild.create_category(name)
			ticket_config["categories"][category.id] = {
				"name": name,
				"description": description
			}
			await save_ticket_data()
			await interaction.response.send_message(f"Category '{name}' created successfully!", ephemeral=True)
		except Exception as e:
			await interaction.response.send_message(f"Error creating category: {str(e)}", ephemeral=True)

class ManageCategoriesView(discord.ui.View):
	def __init__(self):
		super().__init__(timeout=None)
		self.update_options()

	def update_options(self):
		# Clear existing items
		self.clear_items()
		
		# Add category button
		add_button = discord.ui.Button(label="Add Category", style=discord.ButtonStyle.green, custom_id="add_category")
		add_button.callback = self.add_category_callback
		self.add_item(add_button)

		# Add remove buttons for each category
		for cat_id, info in ticket_config["categories"].items():
			remove_button = discord.ui.Button(
				label=f"Remove {info['name']}", 
				style=discord.ButtonStyle.red,
				custom_id=f"remove_category_{cat_id}"
			)
			remove_button.callback = self.remove_category_callback
			self.add_item(remove_button)

	async def add_category_callback(self, interaction: discord.Interaction):
		await interaction.response.send_modal(CategoryModal())

	async def remove_category_callback(self, interaction: discord.Interaction):
		category_id = int(interaction.custom_id.split("_")[-1])
		if category_id in ticket_config["categories"]:
			# Delete Discord category
			category = interaction.guild.get_channel(category_id)
			if category:
				try:
					await category.delete()
				except:
					pass  # Category might already be deleted
			
			# Remove from config
			del ticket_config["categories"][category_id]
			await save_ticket_data()
			
			# Update view
			self.update_options()
			await interaction.response.edit_message(view=self)
			await interaction.followup.send(f"Category removed successfully!", ephemeral=True)


# Active tickets storage
active_tickets = {}

class TicketView(discord.ui.View):
	def __init__(self):
		super().__init__(timeout=None)
		
		# Add close ticket button
		close_button = discord.ui.Button(
			label="Close",
			style=discord.ButtonStyle.grey,
			custom_id="close_ticket",
			emoji="🔒"
		)
		self.add_item(close_button)


	async def button_callback(self, interaction: discord.Interaction):
		try:
			# Extract category_id from the button's custom_id
			category_id = interaction.data["custom_id"].split("_")[-1]
			await create_ticket(interaction, category_id)
			
		except Exception as e:
			print(f"Error in button callback: {e}")
			await interaction.response.send_message("An error occurred while processing your request.", ephemeral=True)

class TicketChannel(View):
	def __init__(self):
		super().__init__(timeout=None)
		
		# Add ticket management buttons
		close_btn = Button(label="Close", style=discord.ButtonStyle.red, custom_id="close_ticket")
		close_reason_btn = Button(label="Close With Reason", style=discord.ButtonStyle.red, custom_id="close_with_reason")
		
		self.add_item(close_btn)
		self.add_item(close_reason_btn)
		# Removed claim button as it's not functional


async def create_ticket(interaction: discord.Interaction, category_id: str):
	try:
		global ticket_counter
		ticket_counter += 1
		
		guild = interaction.guild
		category = guild.get_channel(int(category_id))
		
		if not category:
			await interaction.response.send_message("Invalid ticket category! Please contact an administrator.", ephemeral=True)
			return
			
		# Create the ticket channel with number
		channel = await guild.create_text_channel(
			f"ticket-{ticket_counter:04d}",  # Format: ticket-0001
			category=category,
			topic=f"Ticket for {interaction.user.name}"
		)
		
		# Set permissions
		await channel.set_permissions(interaction.user, read_messages=True, send_messages=True)
		await channel.set_permissions(guild.default_role, read_messages=False)
		
		for role_id in ticket_config.get("staff_roles", []):
			role = guild.get_role(role_id)
			if role:
				await channel.set_permissions(role, read_messages=True, send_messages=True)
		
		# Store ticket info
		active_tickets[str(channel.id)] = {
			"user_id": interaction.user.id,
			"ticket_number": ticket_counter,
			"claimed_by": None
		}
		
		await save_ticket_data()
		
		# Show modal first
		modal = TicketModal(category_id)
		await interaction.response.send_modal(modal)
		
	except Exception as e:
		print(f"Error creating ticket: {e}")
		try:
			await interaction.response.send_message("An error occurred while creating the ticket.", ephemeral=True)
		except:
			pass

async def save_ticket_data():
	"""Save ticket configuration and data"""
	try:
		data = {
			'config': ticket_config,
			'active_tickets': active_tickets,
			'last_ticket_number': ticket_config.get("last_ticket_number", 0)
		}
		
		# Save to temporary file first
		temp_path = "ticket_config.json.tmp"
		with open(temp_path, 'w', encoding='utf-8') as f:
			json.dump(data, f, indent=4)
			
		# Atomic rename
		os.replace(temp_path, "ticket_config.json")
		print(f"Saved ticket configuration successfully: {data}")
		return True
		
	except Exception as e:
		print(f"Error saving ticket data: {e}")
		traceback.print_exc()
		return False




async def load_ticket_data():
	"""Load ticket configuration and data"""
	global ticket_config, active_tickets
	
	try:
		if os.path.exists("ticket_config.json"):
			with open("ticket_config.json", "r", encoding='utf-8') as f:
				data = json.load(f)
				
				# Load config
				if 'config' in data:
					ticket_config.update(data['config'])
					
				# Load active tickets and counter
				active_tickets.update(data.get('active_tickets', {}))
				
				# Load last ticket number
				if 'last_ticket_number' in data:
					ticket_config['last_ticket_number'] = data['last_ticket_number']
				
				print(f"Loaded ticket configuration: {ticket_config}")
				print(f"Loaded active tickets: {active_tickets}")
				print(f"Loaded last ticket number: {ticket_config.get('last_ticket_number', 0)}")
				
				# Recreate panel if ticket channel exists
				if ticket_config.get("ticket_channel"):
					channel = bot.get_channel(ticket_config["ticket_channel"])
					if channel:
						await create_ticket_panel(channel)
						print("Recreated ticket panel successfully")
					else:
						print(f"Warning: Could not find ticket channel {ticket_config['ticket_channel']}")
				
		return True
		
	except Exception as e:
		print(f"Error loading ticket data: {e}")
		traceback.print_exc()
		return False





async def setup_ticket_system(ctx, ticket_channel=None, transcript_channel=None):
	"""Setup the ticket system with categories and options"""
	if isinstance(ctx, discord.Interaction):
		await ctx.response.defer()
		
		embed = discord.Embed(
			title="🎫 Ticket System Setup",
			description="Manage ticket categories and settings below:",
			color=discord.Color.blue()
		)
		
		# Add current configuration info
		if ticket_config["transcript_channel"]:
			transcript_ch = ctx.guild.get_channel(ticket_config["transcript_channel"])
			if transcript_ch:
				embed.add_field(name="Transcript Channel", value=transcript_ch.mention)
		
		if ticket_config["staff_roles"]:
			staff_roles = [ctx.guild.get_role(role_id).mention for role_id in ticket_config["staff_roles"] if ctx.guild.get_role(role_id)]
			if staff_roles:
				embed.add_field(name="Staff Roles", value=", ".join(staff_roles))
		
		view = ManageCategoriesView()
		await ctx.followup.send(embed=embed, view=view)




async def create_ticket_panel(channel):
	try:
		# Create the main embed
		embed = discord.Embed(
			title="📝 Support Tickets",
			description="Click a button below to create a ticket in the corresponding category.",
			color=discord.Color.blue()
		)

		# Create view for buttons
		view = discord.ui.View(timeout=None)
		
		# Add buttons for each category
		for category_id, category_info in ticket_config.get("categories", {}).items():
			button = discord.ui.Button(
				label=category_info["name"],
				custom_id=f"ticket_category_{category_id}",
				style=discord.ButtonStyle.primary,
				emoji="🎫"
			)
			view.add_item(button)
			
			# Add category description to embed
			embed.add_field(
				name=category_info["name"],
				value=category_info.get("description", "No description"),
				inline=False
			)

		# Clear existing messages with rate limiting
		try:
			async for message in channel.history(limit=100):
				if message.author == bot.user:
					await ticket_rate_limiter.execute(
						'delete_message',
						message.delete
					)
		except Exception as e:
			print(f"Error clearing messages: {e}")

		# Send the new panel with rate limiting
		await ticket_rate_limiter.execute(
			'send_message',
			channel.send,
			embed=embed,
			view=view
		)
		return True

	except Exception as e:
		print(f"Error creating ticket panel: {e}")
		import traceback
		traceback.print_exc()
		return False







async def create_ticket(interaction: discord.Interaction, category_id: int):
	"""Create a new ticket"""
	try:
		# Debug prints
		print(f"Processing ticket category: {category_id}")
		print(f"Available categories: {ticket_config.get('categories', {})}")
		
		# Convert category_id to string for comparison since JSON stores keys as strings
		category_id_str = str(category_id)
		
		# Verify category exists
		if category_id_str not in ticket_config.get("categories", {}):
			print(f"Category {category_id} not found in config")
			await interaction.response.send_message(
				"This ticket category no longer exists. Please contact an administrator.",
				ephemeral=True
			)
			return

		# Get the next ticket number from config
		if "last_ticket_number" not in ticket_config:
			ticket_config["last_ticket_number"] = 0
			
		next_ticket_number = ticket_config["last_ticket_number"] + 1
		
		# Verify category exists in Discord
		category = interaction.guild.get_channel(int(category_id))
		if not category:
			await interaction.response.send_message(
				"The ticket category no longer exists in Discord. Please contact an administrator.",
				ephemeral=True
			)
			return

		# Create channel with error handling
		try:
			channel = await interaction.guild.create_text_channel(
				f"ticket-{next_ticket_number:04d}",
				category=category,
				topic=f"Ticket #{next_ticket_number:04d} | {interaction.user.name}"
			)
		except discord.Forbidden:
			await interaction.response.send_message(
				"I don't have permission to create channels. Please contact an administrator.",
				ephemeral=True
			)
			return
		except Exception as e:
			print(f"Error creating channel: {e}")
			await interaction.response.send_message(
				"An error occurred while creating the ticket channel.",
				ephemeral=True
			)
			return

		# Verify channel was created
		if not channel:
			await interaction.response.send_message(
				"Failed to create ticket channel. Please try again later.",
				ephemeral=True
			)
			return

		# Update ticket number only after successful channel creation
		ticket_config["last_ticket_number"] = next_ticket_number
		await save_ticket_data()

		# Set permissions with error handling
		try:
			await channel.set_permissions(interaction.user, read_messages=True, send_messages=True)
			await channel.set_permissions(interaction.guild.default_role, read_messages=False)
			
			# Set staff role permissions
			for role_id in ticket_config.get("staff_roles", []):
				role = interaction.guild.get_role(role_id)
				if role:
					await channel.set_permissions(role, read_messages=True, send_messages=True)
		except discord.Forbidden:
			await channel.delete()
			await interaction.response.send_message(
				"I don't have permission to set channel permissions. Please contact an administrator.",
				ephemeral=True
			)
			return
		except Exception as e:
			await channel.delete()
			print(f"Error setting permissions: {e}")
			await interaction.response.send_message(
				"An error occurred while setting up the ticket channel.",
				ephemeral=True
			)
			return

		# Create and send modal
		modal = TicketModal(category_id, next_ticket_number)
		await interaction.response.send_modal(modal)

	except Exception as e:
		print(f"Error creating ticket: {e}")
		traceback.print_exc()
		try:
			await interaction.followup.send(
				"An error occurred while creating the ticket.",
				ephemeral=True
			)
		except:
			print("Could not send error message")





def get_ticket_commands():
	return [setup_ticket_system]

async def claim_ticket(channel_id: int, staff_member: discord.Member):
	"""Claim a ticket"""
	try:
		if str(channel_id) not in active_tickets:
			return False, "This is not a ticket channel"
			
		ticket_data = active_tickets[str(channel_id)]
		
		# Check if ticket is already claimed
		if ticket_data.get('claimed_by'):
			return False, "Ticket is already claimed"
			
		channel = bot.get_channel(channel_id)
		if not channel:
			return False, "Channel not found"

		# Update ticket data
		ticket_data['claimed_by'] = staff_member.id
		await save_ticket_data()

		# Create claim embed
		embed = discord.Embed(
			title="Ticket Claimed",
			description=f"This ticket has been claimed by {staff_member.mention}",
			color=discord.Color.green()
		)
		
		# Send claim notification
		await channel.send(embed=embed)
		
		# Update original ticket message if it exists
		if 'message_id' in ticket_data:
			try:
				message = await channel.fetch_message(ticket_data['message_id'])
				if message:
					original_embed = message.embeds[0]
					original_embed.add_field(
						name="Claimed By",
						value=staff_member.mention,
						inline=False
					)
					await message.edit(embed=original_embed)
			except:
				pass  # Original message might be deleted or inaccessible

		return True, None

	except Exception as e:
		print(f"Error claiming ticket: {e}")
		traceback.print_exc()
		return False, str(e)

async def reopen_ticket(channel_id: int, user):
	"""Reopen a closed ticket with optimized flow"""
	try:
		channel = user.guild.get_channel(channel_id)
		if not channel:
			return False, "Channel not found"

		if not channel.name.startswith('closed-'):
			return False, "This ticket is not closed"

		# Extract ticket number and prepare new name
		ticket_number = channel.name.split('-')[1]
		new_name = f"ticket-{ticket_number}"

		# Update channel name first
		await asyncio.sleep(1)  # Small initial delay
		await channel.edit(name=new_name)
		await asyncio.sleep(2)  # Wait between operations

		# Update permissions
		overwrites = {
			channel.guild.default_role: discord.PermissionOverwrite(read_messages=False),
			user.guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True)
		}
		
		for role_id in ticket_config.get("staff_roles", []):
			role = channel.guild.get_role(role_id)
			if role:
				overwrites[role] = discord.PermissionOverwrite(read_messages=True, send_messages=True)

		await channel.edit(overwrites=overwrites)
		await asyncio.sleep(2)

		# Create single embed with all controls
		embed = discord.Embed(
			description=f"Ticket Controls\nOpened by {user.mention}",
			color=discord.Color.green()
		)

		view = View()
		close_btn = Button(
			label="Close",
			style=discord.ButtonStyle.red,
			custom_id="close_ticket",
			emoji="🔒"
		)
		delete_btn = Button(
			label="Delete",
			style=discord.ButtonStyle.red,
			custom_id="delete_ticket",
			emoji="🗑️"
		)
		transcript_btn = Button(
			label="Transcript",
			style=discord.ButtonStyle.blurple,
			custom_id="view_transcript",
			emoji="📜"
		)

		view.add_item(close_btn)
		view.add_item(delete_btn)
		view.add_item(transcript_btn)

		await channel.send(embed=embed, view=view)

		# Update ticket status
		channel_id_str = str(channel_id)
		active_tickets[channel_id_str] = {
			"status": "open",
			"reopened_by": user.id,
			"reopened_at": datetime.now().isoformat()
		}
		await save_ticket_data()
		
		return True, None

	except Exception as e:
		print(f"Error reopening ticket: {e}")
		return False, str(e)






class TicketOperationQueue:
	def __init__(self):
		self.queue = asyncio.Queue()
		self._running = False
		self.processing_lock = asyncio.Lock()
		self.operation_delays = {
			'rename': 2.0,
			'permissions': 1.0,
			'message': 0.5
		}

	async def add_operation(self, operation_type, func, *args, **kwargs):
		await self.queue.put((operation_type, func, args, kwargs))
		if not self._running:
			self._running = True
			asyncio.create_task(self._process_queue())

	async def _process_queue(self):
		while True:
			try:
				if self.queue.empty():
					self._running = False
					break

				async with self.processing_lock:
					op_type, func, args, kwargs = await self.queue.get()
					try:
						await func(*args, **kwargs)
						await asyncio.sleep(self.operation_delays.get(op_type, 1.0))
					except Exception as e:
						print(f"Error in operation {op_type}: {e}")
					finally:
						self.queue.task_done()
			except Exception as e:
				print(f"Queue processing error: {e}")
				await asyncio.sleep(1)




async def close_ticket(channel_id: int, reason: str = None, closer: discord.Member = None):
	"""Close a ticket with proper handling"""
	try:
		channel = closer.guild.get_channel(channel_id)
		if not channel:
			return False, "Channel not found"

		# First send transcript if enabled
		if ticket_config.get("transcript_channel"):
			try:
				transcript_channel = closer.guild.get_channel(ticket_config["transcript_channel"])
				if transcript_channel:
					messages = []
					async for message in channel.history(limit=None, oldest_first=True):
						timestamp = message.created_at.strftime("%Y-%m-%d %H:%M:%S")
						messages.append(f"[{timestamp}] {message.author}: {message.content}")
					
					transcript_content = "\n".join(messages)
					transcript_file = discord.File(
						io.StringIO(transcript_content),
						filename=f"transcript-{channel.name}.txt"
					)

					transcript_embed = discord.Embed(
						title="Ticket Transcript",
						description=f"Ticket: {channel.name}\nClosed by: {closer.mention}\nReason: {reason or 'No reason provided'}",
						color=0x2b2d31,
						timestamp=datetime.now()
					)
					
					await ticket_rate_limiter.execute(
						'send_transcript',
						transcript_channel.send,
						embed=transcript_embed,
						file=transcript_file
					)
			except Exception as e:
				print(f"Error sending transcript: {e}")

		# Update channel name with rate limiting
		new_name = f"closed-{channel.name.split('-', 1)[1]}"
		await ticket_rate_limiter.execute(
			'edit_channel',
			channel.edit,
			name=new_name
		)

		# Update permissions with rate limiting
		overwrites = {
			channel.guild.default_role: discord.PermissionOverwrite(read_messages=False),
			closer.guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True)
		}
		
		for role_id in ticket_config.get("staff_roles", []):
			role = channel.guild.get_role(role_id)
			if role:
				overwrites[role] = discord.PermissionOverwrite(read_messages=True, send_messages=True)

		await ticket_rate_limiter.execute(
			'edit_channel',
			channel.edit,
			overwrites=overwrites
		)

		# Send closure message with buttons
		close_embed = discord.Embed(
			title="Ticket Closed",
			description=f"Ticket closed by {closer.mention}\nReason: {reason or 'No reason provided'}",
			color=0x2b2d31
		)

		view = View()
		
		# Add Reopen button
		reopen_btn = Button(
			label="Reopen",
			style=discord.ButtonStyle.green,
			custom_id="reopen_ticket",
			emoji="🔓"
		)
		
		# Add Delete button
		delete_btn = Button(
			label="Delete",
			style=discord.ButtonStyle.red,
			custom_id="delete_ticket",
			emoji="🗑️"
		)
		
		# Add Transcript button
		transcript_btn = Button(
			label="Transcript",
			style=discord.ButtonStyle.blurple,
			custom_id="view_transcript",
			emoji="📜"
		)

		view.add_item(reopen_btn)
		view.add_item(delete_btn)
		view.add_item(transcript_btn)

		await channel.send(embed=close_embed, view=view)

		# Update ticket status
		channel_id_str = str(channel_id)
		active_tickets[channel_id_str] = {
			"status": "closed",
			"closed_by": closer.id,
			"closed_at": datetime.now().isoformat(),
			"reason": reason
		}
		await save_ticket_data()
		
		return True, None

	except Exception as e:
		print(f"Error closing ticket: {e}")
		traceback.print_exc()
		return False, str(e)




class CloseConfirmation(discord.ui.View):
	def __init__(self):
		super().__init__(timeout=30)
		self.value = None

	@discord.ui.button(label="Close", style=discord.ButtonStyle.red)
	async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
		self.value = True
		await interaction.response.defer()
		self.stop()

	@discord.ui.button(label="Cancel", style=discord.ButtonStyle.grey)
	async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
		self.value = False
		await interaction.response.send_message("Ticket closure cancelled.", ephemeral=True)
		self.stop()

async def handle_close_button(interaction: discord.Interaction):
	"""Handle the close button click with proper confirmation"""
	view = CloseConfirmation()
	await interaction.response.send_message(
		"Are you sure you would like to close this ticket?",
		view=view,
		ephemeral=True
	)
	
	await view.wait()
	if view.value:
		success, error = await close_ticket(interaction.channel_id, closer=interaction.user)
		if not success:
			await interaction.followup.send(f"Error closing ticket: {error}", ephemeral=True)








async def reopen_callback(interaction: discord.Interaction):
	try:
		# Check if user has staff role
		has_staff_role = False
		for role_id in ticket_config["staff_roles"]:
			role = interaction.guild.get_role(role_id)
			if role and role in interaction.user.roles:
				has_staff_role = True
				break
		
		if not has_staff_role:
			await interaction.response.send_message("You don't have permission to reopen tickets.", ephemeral=True)
			return

		# Defer the response immediately
		await interaction.response.defer(ephemeral=True)
		
		success, error = await reopen_ticket(interaction.channel.id, interaction.user)
		
		if success:
			await interaction.followup.send("Ticket reopened successfully!", ephemeral=True)
		else:
			await interaction.followup.send(f"Error reopening ticket: {error}", ephemeral=True)
		
	except Exception as e:
		print(f"Error in reopen_callback: {e}")
		await interaction.followup.send(f"Error reopening ticket: {str(e)}", ephemeral=True)


async def setup_buttons(channel, close_embed):
	view = View()
	
	# Add Reopen button
	reopen_btn = Button(label="🔓 Reopen", custom_id="reopen_ticket", style=discord.ButtonStyle.green)
	reopen_btn.callback = reopen_callback
	view.add_item(reopen_btn)

	# Add Transcript button
	transcript_btn = Button(
		label="📜 Transcript",
		style=discord.ButtonStyle.blurple,
		custom_id="view_transcript"
	)
	transcript_btn.callback = handle_transcript_button
	view.add_item(transcript_btn)

	# Add Delete Channel button
	delete_channel_btn = discord.ui.Button(
		label="🗑️ Delete",
		style=discord.ButtonStyle.red,
		custom_id="delete_channel"
	)

	async def delete_channel_callback(interaction: discord.Interaction):
		try:
			# Check if user has staff role
			has_staff_role = False
			for role_id in ticket_config["staff_roles"]:
				role = interaction.guild.get_role(role_id)
				if role and role in interaction.user.roles:
					has_staff_role = True
					break
			
			if not has_staff_role:
				await interaction.response.send_message(
					"You don't have permission to delete tickets.", 
					ephemeral=True
				)
				return

			await channel.delete()
		except discord.Forbidden:
			await interaction.response.send_message(
				"Missing permissions to delete channel.", 
				ephemeral=True
			)
		except Exception as e:
			await interaction.response.send_message(
				f"Error deleting channel: {str(e)}", 
				ephemeral=True
			)

	delete_channel_btn.callback = delete_channel_callback
	view.add_item(delete_channel_btn)

	try:
		await channel.send(embed=close_embed, view=view)
		return True, None
	except Exception as e:
		print(f"Error setting up buttons: {e}")
		return False, str(e)


async def handle_transcript_button(interaction: discord.Interaction):
    """Handle transcript button click"""
    try:
        # Check if user has staff role
        has_staff_role = False
        for role_id in ticket_config["staff_roles"]:
            role = interaction.guild.get_role(role_id)
            if role and role in interaction.user.roles:
                has_staff_role = True
                break
        
        if not has_staff_role:
            await interaction.response.send_message(
                "You don't have permission to view transcripts.", 
                ephemeral=True
            )
            return

        # Defer response while we gather messages
        await interaction.response.defer(ephemeral=True)
        
        # Gather messages
        messages = []
        async for message in interaction.channel.history(limit=None, oldest_first=True):
            timestamp = message.created_at.strftime("%Y-%m-%d %H:%M:%S")
            content = message.content
            # Handle embeds
            if message.embeds:
                for embed in message.embeds:
                    content += f"\nEmbed: {embed.description}"
            messages.append(f"[{timestamp}] {message.author}: {content}")

        transcript_content = "\n".join(messages)

        # Create embed for transcript
        transcript_embed = discord.Embed(
            title=f"Transcript for {interaction.channel.name}",
            color=0x2b2d31,
            timestamp=datetime.now()
        )

        # Split transcript into chunks if it's too long
        chunks = [transcript_content[i:i+4000] for i in range(0, len(transcript_content), 4000)]
        
        # Send first chunk in embed, rest as files if needed
        transcript_embed.description = chunks[0]
        
        if len(chunks) > 1:
            # Create file for remaining chunks
            remaining_content = "\n".join(chunks[1:])
            transcript_file = discord.File(
                io.StringIO(remaining_content),
                filename=f"transcript-{interaction.channel.name}-continued.txt"
            )
            await interaction.followup.send(
                embed=transcript_embed,
                file=transcript_file,
                ephemeral=True
            )
        else:
            await interaction.followup.send(
                embed=transcript_embed,
                ephemeral=True
            )

    except Exception as e:
        print(f"Error viewing transcript: {e}")
        await interaction.followup.send(
            "An error occurred while viewing the transcript.",
            ephemeral=True
        )

async def add_category(guild, name: str, description: str):
	"""Add a new ticket category"""
	try:
		# Create category in Discord
		category = await guild.create_category(name)
		
		# Add to ticket config
		ticket_config["categories"][str(category.id)] = {
			"name": name,
			"description": description
		}
		
		# Save changes
		await save_ticket_data()
		
		print(f"Added category {name} with ID {category.id}")
		print(f"Updated categories: {ticket_config['categories']}")
		
		# Update ticket panel
		if ticket_config.get("ticket_channel"):
			channel = bot.get_channel(ticket_config["ticket_channel"])
			if channel:
				await create_ticket_panel(channel)
				
		return True, category
		
	except Exception as e:
		print(f"Error adding category: {e}")
		traceback.print_exc()
		return False, str(e)


async def remove_category(category_id: int):
	"""Remove a ticket category"""
	if category_id not in ticket_config["categories"]:
		return False, "Category not found"

	try:
		del ticket_config["categories"][category_id]
		await save_ticket_data()
		return True, None
	except Exception as e:
		return False, str(e)

async def set_staff_role(role_id: int):
	"""Add a staff role to the ticket system"""
	if "staff_roles" not in ticket_config:
		ticket_config["staff_roles"] = []
		
	if role_id in ticket_config["staff_roles"]:
		return False
	
	ticket_config["staff_roles"].append(role_id)
	await save_ticket_data()
	return True

async def set_transcript_channel(channel_id: int):
	"""Set the transcript channel for closed tickets"""
	ticket_config["transcript_channel"] = channel_id
	await save_ticket_data()
	return True

async def get_channel(channel_id: int):
	"""Helper function to get channel from ID"""
	for guild in bot.guilds:
		channel = guild.get_channel(channel_id)
		if channel:
			return channel
	return None

async def set_ticket_channel(channel_id: int):
	"""Set the channel where users can create tickets"""
	try:
		ticket_config["ticket_channel"] = channel_id
		await save_ticket_data()
		
		# Get channel and create panel
		channel = bot.get_channel(channel_id)
		if channel:
			await create_ticket_panel(channel)
			
		return True
	except Exception as e:
		print(f"Error setting ticket channel: {e}")
		return False
